<?php
/**
 * Database Configuration
 * Multi-Tenant Print-on-Demand Platform
 */

// Load database configuration
require_once __DIR__ . '/database-config.php';

// PDO options
$pdo_options = [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES => false,
    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
];

// Database connection class
class Database {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, $GLOBALS['pdo_options']);
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            die("Database connection failed. Please check your configuration.");
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    // Prevent cloning
    private function __clone() {}
    
    // Prevent unserialization
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

// Helper function to get database connection
function getDB() {
    return Database::getInstance()->getConnection();
}

// Test database connection
function testDatabaseConnection() {
    try {
        $db = getDB();
        $stmt = $db->query("SELECT 1");
        return true;
    } catch (Exception $e) {
        error_log("Database test failed: " . $e->getMessage());
        return false;
    }
}

// Test database connection without selecting database (for setup)
function testDatabaseConnectionWithoutDB() {
    try {
        $dsn = "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET;
        $pdo = new PDO($dsn, DB_USER, DB_PASS, $GLOBALS['pdo_options']);
        return true;
    } catch (Exception $e) {
        error_log("Database server connection failed: " . $e->getMessage());
        return false;
    }
}

// Create database if it doesn't exist
function createDatabaseIfNotExists() {
    try {
        $dsn = "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET;
        $pdo = new PDO($dsn, DB_USER, DB_PASS, $GLOBALS['pdo_options']);

        $sql = "CREATE DATABASE IF NOT EXISTS `" . DB_NAME . "` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
        $pdo->exec($sql);

        return true;
    } catch (Exception $e) {
        error_log("Database creation failed: " . $e->getMessage());
        return false;
    }
}

// Execute SQL file (for setup)
function executeSQLFile($filepath) {
    try {
        $db = getDB();
        $sql = file_get_contents($filepath);

        // Split SQL into individual statements
        $statements = array_filter(
            array_map('trim', explode(';', $sql)),
            function($stmt) { return !empty($stmt) && !preg_match('/^--/', $stmt); }
        );

        foreach ($statements as $statement) {
            if (!empty($statement)) {
                $db->exec($statement);
            }
        }

        return true;
    } catch (Exception $e) {
        error_log("SQL execution failed: " . $e->getMessage());
        return false;
    }
}

// Execute SQL file with detailed error reporting (for setup)
function executeSQLFileWithDetails($filepath) {
    $result = [
        'success' => false,
        'error' => null,
        'messages' => []
    ];

    try {
        if (!file_exists($filepath)) {
            $result['error'] = "SQL file not found: $filepath";
            return $result;
        }

        $db = getDB();
        $sql = file_get_contents($filepath);

        if (empty($sql)) {
            $result['error'] = "SQL file is empty";
            return $result;
        }

        // Remove comments and split into statements
        $sql = preg_replace('/--.*$/m', '', $sql); // Remove single-line comments
        $sql = preg_replace('/\/\*.*?\*\//s', '', $sql); // Remove multi-line comments

        // Split SQL into individual statements
        $statements = array_filter(
            array_map('trim', explode(';', $sql)),
            function($stmt) { return !empty($stmt); }
        );

        $result['messages'][] = "Found " . count($statements) . " SQL statements to execute";

        $executed = 0;
        foreach ($statements as $statement) {
            if (!empty($statement)) {
                try {
                    $db->exec($statement);
                    $executed++;
                } catch (Exception $e) {
                    // Log the error but continue with other statements
                    error_log("SQL statement failed: " . $e->getMessage());
                    error_log("Statement: " . substr($statement, 0, 100) . "...");

                    // For setup, we'll be more lenient with errors
                    if (strpos($e->getMessage(), 'already exists') === false &&
                        strpos($e->getMessage(), 'Duplicate entry') === false) {
                        $result['error'] = $e->getMessage();
                        return $result;
                    }
                }
            }
        }

        $result['messages'][] = "Successfully executed $executed statements";
        $result['success'] = true;

        return $result;

    } catch (Exception $e) {
        error_log("SQL file execution failed: " . $e->getMessage());
        $result['error'] = $e->getMessage();
        return $result;
    }
}

// Database utility functions
class DatabaseUtils {
    
    public static function tableExists($tableName) {
        try {
            $db = getDB();
            $stmt = $db->prepare("SHOW TABLES LIKE ?");
            $stmt->execute([$tableName]);
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            return false;
        }
    }
    
    public static function getTableCount($tableName) {
        try {
            $db = getDB();
            $stmt = $db->prepare("SELECT COUNT(*) as count FROM `$tableName`");
            $stmt->execute();
            $result = $stmt->fetch();
            return $result['count'];
        } catch (Exception $e) {
            return 0;
        }
    }
    
    public static function generateUniqueId($prefix = '', $length = 8) {
        $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, strlen($characters) - 1)];
        }
        return $prefix . $randomString;
    }
    
    public static function sanitizeSlug($string) {
        $slug = strtolower(trim($string));
        $slug = preg_replace('/[^a-z0-9-]/', '-', $slug);
        $slug = preg_replace('/-+/', '-', $slug);
        return trim($slug, '-');
    }
}

// Create tables manually (backup method)
function createTablesManually() {
    try {
        $db = getDB();

        // Users table
        $db->exec("CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(150) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            role ENUM('user', 'admin') DEFAULT 'user',
            avatar_path VARCHAR(255) DEFAULT NULL,
            email_verified BOOLEAN DEFAULT FALSE,
            status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_email (email),
            INDEX idx_role (role),
            INDEX idx_status (status)
        )");

        // Stores table
        $db->exec("CREATE TABLE IF NOT EXISTS stores (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            store_name VARCHAR(100) NOT NULL,
            slug VARCHAR(100) UNIQUE NOT NULL,
            description TEXT,
            logo_path VARCHAR(255) DEFAULT NULL,
            banner_path VARCHAR(255) DEFAULT NULL,
            theme_color VARCHAR(7) DEFAULT '#3B82F6',
            accent_color VARCHAR(7) DEFAULT '#10B981',
            custom_css TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            total_products INT DEFAULT 0,
            total_orders INT DEFAULT 0,
            total_revenue DECIMAL(10,2) DEFAULT 0.00,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_user_id (user_id),
            INDEX idx_slug (slug),
            INDEX idx_active (is_active)
        )");

        // Products table
        $db->exec("CREATE TABLE IF NOT EXISTS products (
            id INT AUTO_INCREMENT PRIMARY KEY,
            store_id INT NOT NULL,
            name VARCHAR(150) NOT NULL,
            description TEXT,
            design_path VARCHAR(255) NOT NULL,
            mockup_path VARCHAR(255) NOT NULL,
            base_price DECIMAL(8,2) NOT NULL DEFAULT 19.99,
            sizes JSON NOT NULL,
            colors JSON NOT NULL,
            design_config JSON,
            tags VARCHAR(255),
            is_active BOOLEAN DEFAULT TRUE,
            views_count INT DEFAULT 0,
            orders_count INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (store_id) REFERENCES stores(id) ON DELETE CASCADE,
            INDEX idx_store_id (store_id),
            INDEX idx_active (is_active),
            INDEX idx_created (created_at)
        )");

        // Orders table
        $db->exec("CREATE TABLE IF NOT EXISTS orders (
            id INT AUTO_INCREMENT PRIMARY KEY,
            order_number VARCHAR(20) UNIQUE NOT NULL,
            product_id INT NOT NULL,
            customer_name VARCHAR(100) NOT NULL,
            customer_email VARCHAR(150) NOT NULL,
            customer_phone VARCHAR(20),
            shipping_address TEXT NOT NULL,
            city VARCHAR(50) NOT NULL,
            state VARCHAR(50) NOT NULL,
            postal_code VARCHAR(20) NOT NULL,
            country VARCHAR(50) DEFAULT 'India',
            selected_size VARCHAR(10) NOT NULL,
            selected_color VARCHAR(50) NOT NULL,
            quantity INT NOT NULL DEFAULT 1,
            unit_price DECIMAL(8,2) NOT NULL,
            total_amount DECIMAL(8,2) NOT NULL,
            status ENUM('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending',
            payment_method ENUM('cod', 'online', 'test') DEFAULT 'cod',
            payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
            notes TEXT,
            tracking_number VARCHAR(100),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
            INDEX idx_product_id (product_id),
            INDEX idx_status (status),
            INDEX idx_order_number (order_number),
            INDEX idx_created (created_at)
        )");

        // User sessions table
        $db->exec("CREATE TABLE IF NOT EXISTS user_sessions (
            id VARCHAR(128) PRIMARY KEY,
            user_id INT NOT NULL,
            ip_address VARCHAR(45),
            user_agent TEXT,
            last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_user_id (user_id),
            INDEX idx_last_activity (last_activity)
        )");

        // Settings table
        $db->exec("CREATE TABLE IF NOT EXISTS settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_key (setting_key)
        )");

        return true;
    } catch (Exception $e) {
        error_log("Manual table creation failed: " . $e->getMessage());
        return false;
    }
}

// Create default admin user and settings
function createDefaultData() {
    try {
        $db = getDB();

        // Check if admin user already exists
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
        $stmt->execute();
        $result = $stmt->fetch();

        if ($result['count'] == 0) {
            // Create default admin user
            // Password is 'admin123' hashed
            $stmt = $db->prepare("INSERT INTO users (name, email, password, role, email_verified, status) VALUES (?, ?, ?, ?, ?, ?)");
            $stmt->execute([
                'Super Admin',
                '<EMAIL>',
                '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
                'admin',
                1,
                'active'
            ]);
        }

        // Check if settings exist
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM settings");
        $stmt->execute();
        $result = $stmt->fetch();

        if ($result['count'] == 0) {
            // Insert default settings
            $defaultSettings = [
                ['site_name', 'PrintShop Platform', 'Website name'],
                ['site_description', 'Multi-tenant print-on-demand platform', 'Website description'],
                ['default_commission_rate', '5.00', 'Default referral commission rate'],
                ['max_file_size', '5242880', 'Maximum file upload size in bytes (5MB)'],
                ['allowed_file_types', 'jpg,jpeg,png,gif', 'Allowed image file extensions'],
                ['default_product_price', '19.99', 'Default product base price'],
                ['currency_symbol', '₹', 'Currency symbol'],
                ['timezone', 'Asia/Kolkata', 'Default timezone']
            ];

            $stmt = $db->prepare("INSERT INTO settings (setting_key, setting_value, description) VALUES (?, ?, ?)");
            foreach ($defaultSettings as $setting) {
                $stmt->execute($setting);
            }
        }

        return true;
    } catch (Exception $e) {
        error_log("Default data creation failed: " . $e->getMessage());
        return false;
    }
}

// Set global PDO options
$GLOBALS['pdo_options'] = $pdo_options;
?>
