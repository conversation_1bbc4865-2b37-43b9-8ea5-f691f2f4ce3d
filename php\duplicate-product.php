<?php
/**
 * Duplicate Product
 * Multi-Tenant Print-on-Demand Platform
 */

require_once '../config/config.php';

// Set JSON response header
header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'User not authenticated']);
    exit;
}

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

try {
    $user = getCurrentUser();
    if (!$user) {
        echo json_encode(['success' => false, 'message' => 'User not found']);
        exit;
    }
    
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['product_id']) || empty($input['product_id'])) {
        echo json_encode(['success' => false, 'message' => 'Product ID is required']);
        exit;
    }
    
    $productId = intval($input['product_id']);
    
    $db = getDB();
    
    // Get product with store verification (ensure user owns the product)
    $stmt = $db->prepare("
        SELECT p.*, s.user_id 
        FROM products p 
        JOIN stores s ON p.store_id = s.id 
        WHERE p.id = ? AND s.user_id = ?
    ");
    $stmt->execute([$productId, $user['id']]);
    $product = $stmt->fetch();
    
    if (!$product) {
        echo json_encode(['success' => false, 'message' => 'Product not found or access denied']);
        exit;
    }
    
    // Copy design and mockup files
    $originalDesignPath = UPLOAD_DESIGNS_PATH . '/' . $product['design_path'];
    $originalMockupPath = UPLOAD_MOCKUPS_PATH . '/' . $product['mockup_path'];
    
    $newDesignFilename = 'design_' . $user['id'] . '_' . time() . '_' . uniqid() . '.png';
    $newMockupFilename = 'mockup_' . $user['id'] . '_' . time() . '_' . uniqid() . '.png';
    
    $newDesignPath = UPLOAD_DESIGNS_PATH . '/' . $newDesignFilename;
    $newMockupPath = UPLOAD_MOCKUPS_PATH . '/' . $newMockupFilename;
    
    if (!copy($originalDesignPath, $newDesignPath)) {
        echo json_encode(['success' => false, 'message' => 'Failed to copy design file']);
        exit;
    }
    
    if (!copy($originalMockupPath, $newMockupPath)) {
        // Clean up design file if mockup copy fails
        unlink($newDesignPath);
        echo json_encode(['success' => false, 'message' => 'Failed to copy mockup file']);
        exit;
    }
    
    // Create duplicate product
    $newName = $product['name'] . ' (Copy)';
    
    $stmt = $db->prepare("
        INSERT INTO products (
            store_id, name, description, design_path, mockup_path, 
            base_price, sizes, colors, design_config, tags, is_active
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $result = $stmt->execute([
        $product['store_id'],
        $newName,
        $product['description'],
        $newDesignFilename,
        $newMockupFilename,
        $product['base_price'],
        $product['sizes'],
        $product['colors'],
        $product['design_config'],
        $product['tags'],
        $product['is_active']
    ]);
    
    if ($result) {
        $newProductId = $db->lastInsertId();
        
        // Update store product count
        $stmt = $db->prepare("UPDATE stores SET total_products = total_products + 1 WHERE id = ?");
        $stmt->execute([$product['store_id']]);
        
        echo json_encode([
            'success' => true,
            'message' => 'Product duplicated successfully',
            'product_id' => $newProductId
        ]);
    } else {
        // Clean up files if database insert fails
        unlink($newDesignPath);
        unlink($newMockupPath);
        echo json_encode(['success' => false, 'message' => 'Failed to duplicate product']);
    }
    
} catch (Exception $e) {
    error_log("Duplicate product error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while duplicating product']);
}
?>
