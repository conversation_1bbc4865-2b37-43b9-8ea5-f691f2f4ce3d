<?php
/**
 * Setup Verification Script
 * Confirms everything is working properly
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/config.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup Verification</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #3B82F6;
            background-color: #f8fafc;
        }
        .success { color: #155724; background-color: #d4edda; border-color: #c3e6cb; }
        .error { color: #721c24; background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { color: #856404; background-color: #fff3cd; border-color: #ffeaa7; }
        .info { color: #0c5460; background-color: #d1ecf1; border-color: #bee5eb; }
        .btn {
            background-color: #3B82F6;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover { background-color: #2563EB; }
        .btn.success { background-color: #059669; }
        .btn.success:hover { background-color: #047857; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 Setup Verification</h1>
        
        <?php
        $allGood = true;
        
        // Test 1: Database Connection
        echo '<div class="test-section">';
        echo '<h3>1. Database Connection</h3>';
        try {
            $db = getDB();
            echo '<p class="success">✓ Database connection successful</p>';
        } catch (Exception $e) {
            echo '<p class="error">✗ Database connection failed: ' . $e->getMessage() . '</p>';
            $allGood = false;
        }
        echo '</div>';
        
        // Test 2: Tables Existence (using reliable method)
        echo '<div class="test-section">';
        echo '<h3>2. Required Tables</h3>';
        
        $requiredTables = ['users', 'stores', 'products', 'orders', 'user_sessions', 'settings'];
        $missingTables = [];
        
        foreach ($requiredTables as $table) {
            if (DatabaseUtils::tableExists($table)) {
                echo '<p class="success">✓ Table "' . $table . '" exists</p>';
            } else {
                echo '<p class="error">✗ Table "' . $table . '" missing</p>';
                $missingTables[] = $table;
                $allGood = false;
            }
        }
        
        if (empty($missingTables)) {
            echo '<p class="success"><strong>✓ All required tables exist!</strong></p>';
        }
        echo '</div>';
        
        // Test 3: Admin User
        echo '<div class="test-section">';
        echo '<h3>3. Admin User</h3>';
        try {
            $db = getDB();
            $stmt = $db->prepare("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
            $stmt->execute();
            $result = $stmt->fetch();
            
            if ($result['count'] > 0) {
                echo '<p class="success">✓ Admin user exists</p>';
                
                // Get admin details
                $stmt = $db->prepare("SELECT name, email FROM users WHERE role = 'admin' LIMIT 1");
                $stmt->execute();
                $admin = $stmt->fetch();
                echo '<p><strong>Admin:</strong> ' . $admin['name'] . ' (' . $admin['email'] . ')</p>';
                echo '<p><strong>Default Password:</strong> admin123</p>';
            } else {
                echo '<p class="error">✗ No admin user found</p>';
                $allGood = false;
            }
        } catch (Exception $e) {
            echo '<p class="error">✗ Error checking admin user: ' . $e->getMessage() . '</p>';
            $allGood = false;
        }
        echo '</div>';
        
        // Test 4: Upload Directories
        echo '<div class="test-section">';
        echo '<h3>4. Upload Directories</h3>';
        
        $uploadDirs = ['uploads', 'uploads/designs', 'uploads/mockups', 'uploads/logos', 'uploads/avatars'];
        foreach ($uploadDirs as $dir) {
            if (is_dir($dir) && is_writable($dir)) {
                echo '<p class="success">✓ Directory "' . $dir . '" exists and is writable</p>';
            } else {
                echo '<p class="error">✗ Directory "' . $dir . '" missing or not writable</p>';
                $allGood = false;
            }
        }
        echo '</div>';
        
        // Test 5: Authentication Test
        echo '<div class="test-section">';
        echo '<h3>5. Authentication System</h3>';
        
        // Test if Auth class is working
        try {
            require_once 'php/auth.php';
            echo '<p class="success">✓ Auth class loaded successfully</p>';
            
            // Test session
            if (session_status() === PHP_SESSION_ACTIVE) {
                echo '<p class="success">✓ Session system active</p>';
            } else {
                echo '<p class="warning">⚠ Session not active (this is normal for this page)</p>';
            }
            
        } catch (Exception $e) {
            echo '<p class="error">✗ Auth system error: ' . $e->getMessage() . '</p>';
            $allGood = false;
        }
        echo '</div>';
        
        // Test 6: Configuration
        echo '<div class="test-section">';
        echo '<h3>6. Configuration</h3>';
        
        echo '<p><strong>Site Name:</strong> ' . SITE_NAME . '</p>';
        echo '<p><strong>Database:</strong> ' . DB_NAME . '</p>';
        echo '<p><strong>Upload URL:</strong> ' . UPLOAD_URL . '</p>';
        echo '<p><strong>PHP Version:</strong> ' . PHP_VERSION . '</p>';
        
        // Check required PHP extensions
        $requiredExtensions = ['pdo', 'pdo_mysql', 'gd', 'json'];
        foreach ($requiredExtensions as $ext) {
            if (extension_loaded($ext)) {
                echo '<p class="success">✓ PHP extension "' . $ext . '" loaded</p>';
            } else {
                echo '<p class="error">✗ PHP extension "' . $ext . '" missing</p>';
                $allGood = false;
            }
        }
        echo '</div>';
        
        // Overall Status
        echo '<div class="test-section ' . ($allGood ? 'success' : 'error') . '">';
        if ($allGood) {
            echo '<h2>🎉 SETUP COMPLETE!</h2>';
            echo '<p><strong>Your PrintShop Platform is ready to use!</strong></p>';
            echo '<p>Everything is working correctly. You can now:</p>';
            echo '<ul>';
            echo '<li>Visit the <a href="index.php">Homepage</a></li>';
            echo '<li>Register new users at <a href="dashboard/register.php">Registration</a></li>';
            echo '<li><NAME_EMAIL> / admin123</li>';
            echo '<li>Access the <a href="admin/index.php">Admin Panel</a></li>';
            echo '</ul>';
        } else {
            echo '<h2>❌ SETUP INCOMPLETE</h2>';
            echo '<p><strong>Some issues need to be resolved.</strong></p>';
            echo '<p>Please check the errors above and:</p>';
            echo '<ul>';
            echo '<li>Run <a href="fix-tables.php">Fix Tables</a> if tables are missing</li>';
            echo '<li>Check database permissions</li>';
            echo '<li>Verify PHP extensions</li>';
            echo '</ul>';
        }
        echo '</div>';
        ?>
        
        <!-- Quick Actions -->
        <div class="test-section">
            <h3>Quick Actions</h3>
            
            <?php if ($allGood): ?>
                <a href="index.php" class="btn success">Go to Homepage</a>
                <a href="dashboard/register.php" class="btn success">Test Registration</a>
                <a href="dashboard/login.php" class="btn success">Test Login</a>
                <a href="admin/index.php" class="btn">Admin Panel</a>
            <?php else: ?>
                <a href="fix-tables.php" class="btn">Fix Tables</a>
                <a href="setup.php" class="btn">Run Setup Again</a>
            <?php endif; ?>
            
            <a href="debug-tables.php" class="btn">Debug Tables</a>
            <a href="test-auth.php" class="btn">Test Authentication</a>
        </div>
        
        <!-- Sample Data -->
        <?php if ($allGood): ?>
        <div class="test-section">
            <h3>Sample Data</h3>
            <p>Want to test with sample data?</p>
            
            <?php if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_sample'])): ?>
                <?php
                try {
                    $db = getDB();
                    
                    // Create a sample user
                    $stmt = $db->prepare("INSERT INTO users (name, email, password, role, email_verified, status) VALUES (?, ?, ?, ?, ?, ?)");
                    $stmt->execute([
                        'John Doe',
                        '<EMAIL>',
                        password_hash('password123', PASSWORD_DEFAULT),
                        'user',
                        1,
                        'active'
                    ]);
                    $userId = $db->lastInsertId();
                    
                    // Create a sample store
                    $stmt = $db->prepare("INSERT INTO stores (user_id, store_name, slug, description, is_active) VALUES (?, ?, ?, ?, ?)");
                    $stmt->execute([
                        $userId,
                        'John\'s T-Shirt Store',
                        'johns-tshirt-store',
                        'Custom designed t-shirts for everyone!',
                        1
                    ]);
                    
                    echo '<p class="success">✓ Sample data created!</p>';
                    echo '<p><strong>Sample User:</strong> <EMAIL> / password123</p>';
                    echo '<p><strong>Sample Store:</strong> <a href="stores/index.php?store=johns-tshirt-store">John\'s T-Shirt Store</a></p>';
                    
                } catch (Exception $e) {
                    echo '<p class="error">✗ Error creating sample data: ' . $e->getMessage() . '</p>';
                }
                ?>
            <?php else: ?>
                <form method="POST">
                    <button type="submit" name="create_sample" class="btn">Create Sample Data</button>
                </form>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
