<?php
/**
 * Admin Panel Dashboard
 * Multi-Tenant Print-on-Demand Platform
 */

require_once '../config/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn()) {
    redirect('../dashboard/login.php');
}

$user = getCurrentUser();
if (!$user || $user['role'] !== 'admin') {
    redirect('../dashboard/index.php');
}

// Get admin dashboard statistics
try {
    $db = getDB();
    
    // Get overall platform statistics
    $stats = [];
    
    // Total users
    $stmt = $db->prepare("SELECT COUNT(*) as total_users FROM users WHERE role = 'user'");
    $stmt->execute();
    $stats['total_users'] = $stmt->fetchColumn();
    
    // Total stores
    $stmt = $db->prepare("SELECT COUNT(*) as total_stores FROM stores");
    $stmt->execute();
    $stats['total_stores'] = $stmt->fetchColumn();
    
    // Total products
    $stmt = $db->prepare("SELECT COUNT(*) as total_products FROM products");
    $stmt->execute();
    $stats['total_products'] = $stmt->fetchColumn();
    
    // Total orders
    $stmt = $db->prepare("SELECT COUNT(*) as total_orders FROM orders");
    $stmt->execute();
    $stats['total_orders'] = $stmt->fetchColumn();
    
    // Total revenue
    $stmt = $db->prepare("SELECT SUM(total_amount) as total_revenue FROM orders WHERE status != 'cancelled'");
    $stmt->execute();
    $stats['total_revenue'] = $stmt->fetchColumn() ?: 0;
    
    // Active stores
    $stmt = $db->prepare("SELECT COUNT(*) as active_stores FROM stores WHERE is_active = 1");
    $stmt->execute();
    $stats['active_stores'] = $stmt->fetchColumn();
    
    // Recent users
    $stmt = $db->prepare("
        SELECT id, name, email, created_at 
        FROM users 
        WHERE role = 'user' 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $recentUsers = $stmt->fetchAll();
    
    // Recent stores
    $stmt = $db->prepare("
        SELECT s.*, u.name as owner_name 
        FROM stores s 
        JOIN users u ON s.user_id = u.id 
        ORDER BY s.created_at DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $recentStores = $stmt->fetchAll();
    
    // Recent orders
    $stmt = $db->prepare("
        SELECT o.*, p.name as product_name, s.store_name, u.name as store_owner
        FROM orders o
        JOIN products p ON o.product_id = p.id
        JOIN stores s ON p.store_id = s.id
        JOIN users u ON s.user_id = u.id
        ORDER BY o.created_at DESC
        LIMIT 10
    ");
    $stmt->execute();
    $recentOrders = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log("Admin dashboard error: " . $e->getMessage());
    $stats = [
        'total_users' => 0,
        'total_stores' => 0,
        'total_products' => 0,
        'total_orders' => 0,
        'total_revenue' => 0,
        'active_stores' => 0
    ];
    $recentUsers = [];
    $recentStores = [];
    $recentOrders = [];
}

$pageTitle = 'Admin Dashboard - ' . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/dashboard.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
</head>
<body>
    <div class="dashboard-layout">
        <?php include '../includes/admin-sidebar.php'; ?>
        
        <main class="dashboard-main">
            <div class="dashboard-header">
                <div>
                    <h1>Admin Dashboard</h1>
                    <p>Platform overview and management</p>
                </div>
                <div class="header-actions">
                    <button class="btn btn-outline" onclick="exportPlatformData()">
                        <i class="icon-download"></i>
                        Export Data
                    </button>
                    <button class="btn btn-primary" onclick="refreshDashboard()">
                        <i class="icon-refresh"></i>
                        Refresh
                    </button>
                </div>
            </div>
            
            <div class="dashboard-content">
                <!-- Platform Statistics -->
                <div class="admin-stats">
                    <div class="stat-card">
                        <div class="stat-header">
                            <h3>Total Users</h3>
                            <div class="stat-icon users">👥</div>
                        </div>
                        <div class="stat-value"><?php echo number_format($stats['total_users']); ?></div>
                        <div class="stat-change">Platform members</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <h3>Active Stores</h3>
                            <div class="stat-icon stores">🏪</div>
                        </div>
                        <div class="stat-value"><?php echo number_format($stats['active_stores']); ?></div>
                        <div class="stat-change">of <?php echo $stats['total_stores']; ?> total</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <h3>Total Products</h3>
                            <div class="stat-icon products">👕</div>
                        </div>
                        <div class="stat-value"><?php echo number_format($stats['total_products']); ?></div>
                        <div class="stat-change">Designs created</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <h3>Total Orders</h3>
                            <div class="stat-icon orders">📦</div>
                        </div>
                        <div class="stat-value"><?php echo number_format($stats['total_orders']); ?></div>
                        <div class="stat-change">Platform orders</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <h3>Total Revenue</h3>
                            <div class="stat-icon revenue">💰</div>
                        </div>
                        <div class="stat-value"><?php echo formatPrice($stats['total_revenue']); ?></div>
                        <div class="stat-change">Platform earnings</div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="admin-actions">
                    <h2>Quick Actions</h2>
                    <div class="actions-grid">
                        <a href="users.php" class="action-card">
                            <div class="action-icon">👥</div>
                            <h3>Manage Users</h3>
                            <p>View and manage platform users</p>
                        </a>
                        
                        <a href="stores.php" class="action-card">
                            <div class="action-icon">🏪</div>
                            <h3>Manage Stores</h3>
                            <p>Monitor and moderate user stores</p>
                        </a>
                        
                        <a href="orders.php" class="action-card">
                            <div class="action-icon">📦</div>
                            <h3>View Orders</h3>
                            <p>Monitor platform orders and transactions</p>
                        </a>
                        
                        <a href="settings.php" class="action-card">
                            <div class="action-icon">⚙️</div>
                            <h3>Platform Settings</h3>
                            <p>Configure platform settings and features</p>
                        </a>
                    </div>
                </div>
                
                <!-- Recent Activity -->
                <div class="admin-sections">
                    <!-- Recent Users -->
                    <div class="admin-section">
                        <div class="section-header">
                            <h2>Recent Users</h2>
                            <a href="users.php" class="btn btn-sm btn-outline">View All</a>
                        </div>
                        
                        <?php if (empty($recentUsers)): ?>
                            <div class="empty-section">
                                <div class="empty-icon">👥</div>
                                <p>No users registered yet</p>
                            </div>
                        <?php else: ?>
                            <div class="users-list">
                                <?php foreach ($recentUsers as $recentUser): ?>
                                    <div class="user-item">
                                        <div class="user-avatar">
                                            <div class="avatar-placeholder">
                                                <?php echo strtoupper(substr($recentUser['name'], 0, 1)); ?>
                                            </div>
                                        </div>
                                        <div class="user-details">
                                            <h4><?php echo htmlspecialchars($recentUser['name']); ?></h4>
                                            <p><?php echo htmlspecialchars($recentUser['email']); ?></p>
                                            <small>Joined <?php echo formatDate($recentUser['created_at'], 'M d, Y'); ?></small>
                                        </div>
                                        <div class="user-actions">
                                            <button class="btn btn-sm btn-outline" onclick="viewUser(<?php echo $recentUser['id']; ?>)">
                                                View
                                            </button>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Recent Stores -->
                    <div class="admin-section">
                        <div class="section-header">
                            <h2>Recent Stores</h2>
                            <a href="stores.php" class="btn btn-sm btn-outline">View All</a>
                        </div>
                        
                        <?php if (empty($recentStores)): ?>
                            <div class="empty-section">
                                <div class="empty-icon">🏪</div>
                                <p>No stores created yet</p>
                            </div>
                        <?php else: ?>
                            <div class="stores-list">
                                <?php foreach ($recentStores as $store): ?>
                                    <div class="store-item">
                                        <div class="store-logo">
                                            <?php if ($store['logo_path']): ?>
                                                <img src="<?php echo UPLOAD_URL . '/logos/' . $store['logo_path']; ?>" alt="Store Logo">
                                            <?php else: ?>
                                                <div class="logo-placeholder">🏪</div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="store-details">
                                            <h4><?php echo htmlspecialchars($store['store_name']); ?></h4>
                                            <p>by <?php echo htmlspecialchars($store['owner_name']); ?></p>
                                            <small>Created <?php echo formatDate($store['created_at'], 'M d, Y'); ?></small>
                                        </div>
                                        <div class="store-status">
                                            <span class="status-badge <?php echo $store['is_active'] ? 'active' : 'inactive'; ?>">
                                                <?php echo $store['is_active'] ? 'Active' : 'Inactive'; ?>
                                            </span>
                                        </div>
                                        <div class="store-actions">
                                            <a href="../stores/index.php?store=<?php echo $store['slug']; ?>" target="_blank" class="btn btn-sm btn-outline">
                                                Visit
                                            </a>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Recent Orders -->
                <?php if (!empty($recentOrders)): ?>
                    <div class="admin-section full-width">
                        <div class="section-header">
                            <h2>Recent Orders</h2>
                            <a href="orders.php" class="btn btn-sm btn-outline">View All</a>
                        </div>
                        
                        <div class="orders-table-container">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>Order #</th>
                                        <th>Product</th>
                                        <th>Store</th>
                                        <th>Customer</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recentOrders as $order): ?>
                                        <tr>
                                            <td><strong>#<?php echo $order['order_number']; ?></strong></td>
                                            <td><?php echo htmlspecialchars($order['product_name']); ?></td>
                                            <td><?php echo htmlspecialchars($order['store_name']); ?></td>
                                            <td><?php echo htmlspecialchars($order['customer_name']); ?></td>
                                            <td><?php echo formatPrice($order['total_amount']); ?></td>
                                            <td>
                                                <span class="status-badge status-<?php echo $order['status']; ?>">
                                                    <?php echo ucfirst($order['status']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo formatDate($order['created_at'], 'M d, Y'); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>
    
    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/admin.js"></script>
</body>
</html>
