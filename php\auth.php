<?php
/**
 * Authentication Handler
 * Multi-Tenant Print-on-Demand Platform
 */

require_once __DIR__ . '/../config/config.php';

class Auth {

    /**
     * Register a new user
     */
    public static function register($name, $email, $password, $confirmPassword) {
        $errors = [];

        // Validation
        if (empty($name) || strlen($name) < 2) {
            $errors[] = "Name must be at least 2 characters long";
        }

        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = "Please enter a valid email address";
        }

        if (empty($password) || strlen($password) < PASSWORD_MIN_LENGTH) {
            $errors[] = "Password must be at least " . PASSWORD_MIN_LENGTH . " characters long";
        }

        if ($password !== $confirmPassword) {
            $errors[] = "Passwords do not match";
        }

        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }

        try {
            $db = getDB();

            // Check if email already exists
            $stmt = $db->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$email]);
            if ($stmt->rowCount() > 0) {
                return ['success' => false, 'errors' => ['Email address is already registered']];
            }

            // Hash password
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

            // Insert user
            $stmt = $db->prepare("INSERT INTO users (name, email, password, role, email_verified, status) VALUES (?, ?, ?, 'user', FALSE, 'active')");
            $stmt->execute([$name, $email, $hashedPassword]);

            $userId = $db->lastInsertId();

            // Create user store
            $storeSlug = DatabaseUtils::sanitizeSlug($name . '-store');
            $originalSlug = $storeSlug;
            $counter = 1;

            // Ensure unique slug
            while (self::storeSlugExists($storeSlug)) {
                $storeSlug = $originalSlug . '-' . $counter;
                $counter++;
            }

            $stmt = $db->prepare("INSERT INTO stores (user_id, store_name, slug, description, theme_color, accent_color, is_active) VALUES (?, ?, ?, ?, '#3B82F6', '#10B981', TRUE)");
            $stmt->execute([$userId, $name . "'s Store", $storeSlug, "Welcome to my custom t-shirt store!"]);

            return ['success' => true, 'user_id' => $userId, 'message' => 'Registration successful! You can now login.'];

        } catch (Exception $e) {
            error_log("Registration error: " . $e->getMessage());
            return ['success' => false, 'errors' => ['Registration failed. Please try again.']];
        }
    }

    /**
     * Login user
     */
    public static function login($email, $password, $rememberMe = false) {
        $errors = [];

        if (empty($email)) {
            $errors[] = "Email is required";
        }

        if (empty($password)) {
            $errors[] = "Password is required";
        }

        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }

        try {
            $db = getDB();

            $stmt = $db->prepare("SELECT id, name, email, password, role, status FROM users WHERE email = ?");
            $stmt->execute([$email]);
            $user = $stmt->fetch();

            if (!$user || !password_verify($password, $user['password'])) {
                return ['success' => false, 'errors' => ['Invalid email or password']];
            }

            if ($user['status'] !== 'active') {
                return ['success' => false, 'errors' => ['Your account has been suspended. Please contact support.']];
            }

            // Create session
            self::createSession($user);

            // Update last login (optional)
            $stmt = $db->prepare("UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = ?");
            $stmt->execute([$user['id']]);

            return ['success' => true, 'user' => $user, 'message' => 'Login successful!'];

        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            return ['success' => false, 'errors' => ['Login failed. Please try again.']];
        }
    }

    /**
     * Create user session
     */
    private static function createSession($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_name'] = $user['name'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['logged_in'] = true;
        $_SESSION['login_time'] = time();

        // Regenerate session ID for security
        session_regenerate_id(true);
    }

    /**
     * Logout user
     */
    public static function logout() {
        // Clear session data
        $_SESSION = [];

        // Destroy session cookie
        if (isset($_COOKIE[session_name()])) {
            setcookie(session_name(), '', time() - 3600, '/');
        }

        // Destroy session
        session_destroy();

        return ['success' => true, 'message' => 'Logged out successfully'];
    }

    /**
     * Check if store slug exists
     */
    private static function storeSlugExists($slug) {
        try {
            $db = getDB();
            $stmt = $db->prepare("SELECT id FROM stores WHERE slug = ?");
            $stmt->execute([$slug]);
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Get user by ID
     */
    public static function getUserById($userId) {
        try {
            $db = getDB();
            $stmt = $db->prepare("SELECT id, name, email, role, avatar_path, status, created_at FROM users WHERE id = ? AND status = 'active'");
            $stmt->execute([$userId]);
            return $stmt->fetch();
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * Update user profile
     */
    public static function updateProfile($userId, $name, $email, $currentPassword = '', $newPassword = '') {
        $errors = [];

        if (empty($name) || strlen($name) < 2) {
            $errors[] = "Name must be at least 2 characters long";
        }

        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = "Please enter a valid email address";
        }

        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }

        try {
            $db = getDB();

            // Get current user data
            $stmt = $db->prepare("SELECT email, password FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $currentUser = $stmt->fetch();

            if (!$currentUser) {
                return ['success' => false, 'errors' => ['User not found']];
            }

            // Check if email is being changed and if it's already taken
            if ($email !== $currentUser['email']) {
                $stmt = $db->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
                $stmt->execute([$email, $userId]);
                if ($stmt->rowCount() > 0) {
                    return ['success' => false, 'errors' => ['Email address is already taken']];
                }
            }

            // Handle password change
            $updatePassword = false;
            $hashedPassword = '';

            if (!empty($newPassword)) {
                if (empty($currentPassword)) {
                    return ['success' => false, 'errors' => ['Current password is required to change password']];
                }

                if (!password_verify($currentPassword, $currentUser['password'])) {
                    return ['success' => false, 'errors' => ['Current password is incorrect']];
                }

                if (strlen($newPassword) < PASSWORD_MIN_LENGTH) {
                    return ['success' => false, 'errors' => ['New password must be at least ' . PASSWORD_MIN_LENGTH . ' characters long']];
                }

                $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                $updatePassword = true;
            }

            // Update user
            if ($updatePassword) {
                $stmt = $db->prepare("UPDATE users SET name = ?, email = ?, password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
                $stmt->execute([$name, $email, $hashedPassword, $userId]);
            } else {
                $stmt = $db->prepare("UPDATE users SET name = ?, email = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
                $stmt->execute([$name, $email, $userId]);
            }

            // Update session data
            $_SESSION['user_name'] = $name;
            $_SESSION['user_email'] = $email;

            return ['success' => true, 'message' => 'Profile updated successfully'];

        } catch (Exception $e) {
            error_log("Profile update error: " . $e->getMessage());
            return ['success' => false, 'errors' => ['Profile update failed. Please try again.']];
        }
    }
}
?>
