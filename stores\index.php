<?php
/**
 * Dynamic Store Frontend
 * Multi-Tenant Print-on-Demand Platform
 */

require_once '../config/config.php';

// Get store slug from URL
$storeSlug = $_GET['store'] ?? '';
$productId = $_GET['product'] ?? null;

if (empty($storeSlug)) {
    // Redirect to main site if no store specified
    redirect('../public-site/index.html');
}

try {
    $db = getDB();

    // Get store information
    $stmt = $db->prepare("SELECT * FROM stores WHERE slug = ? AND is_active = 1");
    $stmt->execute([$storeSlug]);
    $store = $stmt->fetch();

    if (!$store) {
        // Store not found
        http_response_code(404);
        include '../includes/404.php';
        exit;
    }

    // Get store owner
    $stmt = $db->prepare("SELECT name, email FROM users WHERE id = ?");
    $stmt->execute([$store['user_id']]);
    $storeOwner = $stmt->fetch();

    // If specific product requested
    $selectedProduct = null;
    if ($productId) {
        $stmt = $db->prepare("SELECT * FROM products WHERE id = ? AND store_id = ? AND is_active = 1");
        $stmt->execute([$productId, $store['id']]);
        $selectedProduct = $stmt->fetch();

        if ($selectedProduct) {
            // Increment product views
            $stmt = $db->prepare("UPDATE products SET views_count = views_count + 1 WHERE id = ?");
            $stmt->execute([$productId]);
        }
    }

    // Get store products
    $stmt = $db->prepare("
        SELECT * FROM products
        WHERE store_id = ? AND is_active = 1
        ORDER BY created_at DESC
    ");
    $stmt->execute([$store['id']]);
    $products = $stmt->fetchAll();

    // Increment store views
    $stmt = $db->prepare("UPDATE stores SET views_count = views_count + 1 WHERE id = ?");
    $stmt->execute([$store['id']]);

} catch (Exception $e) {
    error_log("Store frontend error: " . $e->getMessage());
    http_response_code(500);
    include '../includes/500.php';
    exit;
}

// Set page title and meta
$pageTitle = $selectedProduct ?
    htmlspecialchars($selectedProduct['name']) . ' - ' . htmlspecialchars($store['store_name']) :
    htmlspecialchars($store['store_name']) . ' - Custom T-Shirts';

$pageDescription = $selectedProduct ?
    htmlspecialchars($selectedProduct['description']) :
    htmlspecialchars($store['description']);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="description" content="<?php echo $pageDescription; ?>">
    <meta name="keywords" content="custom t-shirts, print on demand, <?php echo htmlspecialchars($store['store_name']); ?>">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo $pageTitle; ?>">
    <meta property="og:description" content="<?php echo $pageDescription; ?>">
    <meta property="og:type" content="<?php echo $selectedProduct ? 'product' : 'website'; ?>">
    <meta property="og:url" content="<?php echo getCurrentUrl(); ?>">
    <?php if ($selectedProduct): ?>
        <meta property="og:image" content="<?php echo UPLOAD_URL . '/mockups/' . $selectedProduct['mockup_path']; ?>">
        <meta property="product:price:amount" content="<?php echo $selectedProduct['base_price']; ?>">
        <meta property="product:price:currency" content="<?php echo DEFAULT_CURRENCY; ?>">
    <?php elseif ($store['logo_path']): ?>
        <meta property="og:image" content="<?php echo UPLOAD_URL . '/logos/' . $store['logo_path']; ?>">
    <?php endif; ?>

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo $pageTitle; ?>">
    <meta name="twitter:description" content="<?php echo $pageDescription; ?>">
    <?php if ($selectedProduct): ?>
        <meta name="twitter:image" content="<?php echo UPLOAD_URL . '/mockups/' . $selectedProduct['mockup_path']; ?>">
    <?php endif; ?>

    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/store.css">
    <link rel="icon" type="image/svg+xml" href="../assets/images/favicon.svg">

    <!-- Store Custom Styles -->
    <?php if ($store['custom_css']): ?>
        <style><?php echo $store['custom_css']; ?></style>
    <?php endif; ?>
</head>
<body class="store-page" data-store-theme="<?php echo $store['theme'] ?? 'default'; ?>">
    <!-- Store Header -->
    <header class="store-header">
        <div class="container">
            <div class="store-brand">
                <?php if ($store['logo_path']): ?>
                    <img src="<?php echo UPLOAD_URL . '/logos/' . $store['logo_path']; ?>"
                         alt="<?php echo htmlspecialchars($store['store_name']); ?>"
                         class="store-logo">
                <?php endif; ?>
                <div class="store-info">
                    <h1 class="store-name"><?php echo htmlspecialchars($store['store_name']); ?></h1>
                    <p class="store-tagline"><?php echo htmlspecialchars($store['description']); ?></p>
                </div>
            </div>

            <nav class="store-nav">
                <a href="?store=<?php echo $store['slug']; ?>" class="nav-link <?php echo !$selectedProduct ? 'active' : ''; ?>">
                    All Products
                </a>
                <a href="#about" class="nav-link">About</a>
                <a href="#contact" class="nav-link">Contact</a>
            </nav>
        </div>
    </header>

    <main class="store-main">
        <?php if ($selectedProduct): ?>
            <!-- Single Product View -->
            <div class="container">
                <div class="product-detail">
                    <div class="product-gallery">
                        <div class="main-image">
                            <img src="<?php echo UPLOAD_URL . '/mockups/' . $selectedProduct['mockup_path']; ?>"
                                 alt="<?php echo htmlspecialchars($selectedProduct['name']); ?>"
                                 id="mainProductImage">
                        </div>

                        <!-- Color variations would go here -->
                        <?php
                        $colors = json_decode($selectedProduct['colors'], true);
                        if (count($colors) > 1):
                        ?>
                            <div class="color-options">
                                <h4>Available Colors:</h4>
                                <div class="color-swatches">
                                    <?php foreach ($colors as $color): ?>
                                        <div class="color-swatch"
                                             data-color="<?php echo $color['hex']; ?>"
                                             style="background-color: <?php echo $color['hex']; ?>;"
                                             title="<?php echo $color['name']; ?>">
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="product-info">
                        <nav class="breadcrumb">
                            <a href="?store=<?php echo $store['slug']; ?>">← Back to Store</a>
                        </nav>

                        <h1 class="product-title"><?php echo htmlspecialchars($selectedProduct['name']); ?></h1>
                        <div class="product-price"><?php echo formatPrice($selectedProduct['base_price']); ?></div>

                        <?php if ($selectedProduct['description']): ?>
                            <div class="product-description">
                                <p><?php echo nl2br(htmlspecialchars($selectedProduct['description'])); ?></p>
                            </div>
                        <?php endif; ?>

                        <!-- Order Form -->
                        <form class="order-form" id="orderForm">
                            <input type="hidden" name="product_id" value="<?php echo $selectedProduct['id']; ?>">

                            <div class="form-group">
                                <label for="size">Size:</label>
                                <select name="size" id="size" required class="form-control">
                                    <?php
                                    $sizes = json_decode($selectedProduct['sizes'], true);
                                    foreach ($sizes as $size):
                                    ?>
                                        <option value="<?php echo $size; ?>"><?php echo $size; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="color">Color:</label>
                                <select name="color" id="color" required class="form-control">
                                    <?php foreach ($colors as $color): ?>
                                        <option value="<?php echo $color['name']; ?>"><?php echo $color['name']; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="quantity">Quantity:</label>
                                <input type="number" name="quantity" id="quantity" value="1" min="1" max="10" required class="form-control">
                            </div>

                            <div class="total-price">
                                Total: <span id="totalPrice"><?php echo formatPrice($selectedProduct['base_price']); ?></span>
                            </div>

                            <button type="button" class="btn btn-primary btn-lg btn-full" onclick="proceedToCheckout()">
                                Order Now
                            </button>
                        </form>

                        <!-- Product Stats -->
                        <div class="product-stats">
                            <div class="stat">
                                <span class="stat-value"><?php echo $selectedProduct['views_count']; ?></span>
                                <span class="stat-label">Views</span>
                            </div>
                            <div class="stat">
                                <span class="stat-value"><?php echo $selectedProduct['orders_count']; ?></span>
                                <span class="stat-label">Orders</span>
                            </div>
                        </div>

                        <!-- Share Buttons -->
                        <div class="product-share">
                            <h4>Share this product:</h4>
                            <div class="share-buttons">
                                <a href="#" onclick="shareProduct('facebook')" class="share-btn facebook">📘 Facebook</a>
                                <a href="#" onclick="shareProduct('twitter')" class="share-btn twitter">🐦 Twitter</a>
                                <a href="#" onclick="shareProduct('whatsapp')" class="share-btn whatsapp">💬 WhatsApp</a>
                                <a href="#" onclick="copyProductUrl()" class="share-btn copy">📋 Copy Link</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Related Products -->
                <?php if (count($products) > 1): ?>
                    <section class="related-products">
                        <h2>More from this store</h2>
                        <div class="products-grid">
                            <?php
                            $relatedProducts = array_filter($products, fn($p) => $p['id'] != $selectedProduct['id']);
                            $relatedProducts = array_slice($relatedProducts, 0, 4);
                            foreach ($relatedProducts as $product):
                            ?>
                                <div class="product-card">
                                    <a href="?store=<?php echo $store['slug']; ?>&product=<?php echo $product['id']; ?>" class="product-link">
                                        <div class="product-image">
                                            <img src="<?php echo UPLOAD_URL . '/mockups/' . $product['mockup_path']; ?>"
                                                 alt="<?php echo htmlspecialchars($product['name']); ?>">
                                        </div>
                                        <div class="product-details">
                                            <h3 class="product-name"><?php echo htmlspecialchars($product['name']); ?></h3>
                                            <div class="product-price"><?php echo formatPrice($product['base_price']); ?></div>
                                        </div>
                                    </a>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </section>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <!-- Store Products Grid -->
            <div class="container">
                <?php if (empty($products)): ?>
                    <div class="empty-store">
                        <div class="empty-icon">🎨</div>
                        <h2>Coming Soon!</h2>
                        <p>This store is being set up with amazing custom t-shirt designs. Check back soon!</p>
                    </div>
                <?php else: ?>
                    <div class="store-products">
                        <div class="products-header">
                            <h2>Our Products</h2>
                            <p>Discover our collection of custom designed t-shirts</p>
                        </div>

                        <div class="products-grid">
                            <?php foreach ($products as $product): ?>
                                <div class="product-card">
                                    <a href="?store=<?php echo $store['slug']; ?>&product=<?php echo $product['id']; ?>" class="product-link">
                                        <div class="product-image">
                                            <img src="<?php echo UPLOAD_URL . '/mockups/' . $product['mockup_path']; ?>"
                                                 alt="<?php echo htmlspecialchars($product['name']); ?>">
                                            <div class="product-overlay">
                                                <span class="view-product">View Product</span>
                                            </div>
                                        </div>
                                        <div class="product-details">
                                            <h3 class="product-name"><?php echo htmlspecialchars($product['name']); ?></h3>
                                            <div class="product-price"><?php echo formatPrice($product['base_price']); ?></div>

                                            <?php
                                            $productColors = json_decode($product['colors'], true);
                                            if ($productColors):
                                            ?>
                                                <div class="product-colors">
                                                    <?php foreach (array_slice($productColors, 0, 4) as $color): ?>
                                                        <div class="color-dot" style="background-color: <?php echo $color['hex']; ?>;" title="<?php echo $color['name']; ?>"></div>
                                                    <?php endforeach; ?>
                                                    <?php if (count($productColors) > 4): ?>
                                                        <span class="more-colors">+<?php echo count($productColors) - 4; ?></span>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </a>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </main>

    <!-- Store Footer -->
    <footer class="store-footer" id="about">
        <div class="container">
            <div class="footer-content">
                <div class="store-about">
                    <h3>About <?php echo htmlspecialchars($store['store_name']); ?></h3>
                    <p><?php echo nl2br(htmlspecialchars($store['description'])); ?></p>

                    <?php if ($storeOwner): ?>
                        <div class="store-owner">
                            <p><strong>Store Owner:</strong> <?php echo htmlspecialchars($storeOwner['name']); ?></p>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="store-contact" id="contact">
                    <h3>Contact Us</h3>
                    <div class="contact-info">
                        <?php if ($store['contact_email']): ?>
                            <p><strong>Email:</strong> <a href="mailto:<?php echo $store['contact_email']; ?>"><?php echo $store['contact_email']; ?></a></p>
                        <?php endif; ?>

                        <?php if ($store['contact_phone']): ?>
                            <p><strong>Phone:</strong> <a href="tel:<?php echo $store['contact_phone']; ?>"><?php echo $store['contact_phone']; ?></a></p>
                        <?php endif; ?>

                        <?php if ($store['social_links']): ?>
                            <?php $socialLinks = json_decode($store['social_links'], true); ?>
                            <div class="social-links">
                                <?php foreach ($socialLinks as $platform => $url): ?>
                                    <a href="<?php echo htmlspecialchars($url); ?>" target="_blank" class="social-link">
                                        <?php echo ucfirst($platform); ?>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="store-stats">
                    <h3>Store Stats</h3>
                    <div class="stats-grid">
                        <div class="stat">
                            <span class="stat-value"><?php echo count($products); ?></span>
                            <span class="stat-label">Products</span>
                        </div>
                        <div class="stat">
                            <span class="stat-value"><?php echo $store['views_count']; ?></span>
                            <span class="stat-label">Store Views</span>
                        </div>
                        <div class="stat">
                            <span class="stat-value"><?php echo $store['total_orders']; ?></span>
                            <span class="stat-label">Orders</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; <?php echo date('Y'); ?> <?php echo htmlspecialchars($store['store_name']); ?>. All rights reserved.</p>
                <p>Powered by <a href="../public-site/index.html">PrintShop Platform</a></p>
            </div>
        </div>
    </footer>

    <!-- Checkout Modal -->
    <div id="checkoutModal" class="modal">
        <div class="modal-backdrop">
            <div class="modal-content modal-lg">
                <div class="modal-header">
                    <h3>Complete Your Order</h3>
                    <button class="modal-close" data-modal-close>&times;</button>
                </div>
                <div class="modal-body" id="checkoutModalBody">
                    <!-- Checkout form will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/store.js"></script>

    <!-- Store Custom Scripts -->
    <?php if ($store['custom_js']): ?>
        <script><?php echo $store['custom_js']; ?></script>
    <?php endif; ?>
</body>
</html>
