<?php
/**
 * Admin Sidebar Navigation
 * Multi-Tenant Print-on-Demand Platform
 */

$currentPage = basename($_SERVER['PHP_SELF']);
$user = getCurrentUser();
?>

<aside class="dashboard-sidebar">
    <div class="sidebar-header">
        <div class="logo">
            <h2>Admin Panel</h2>
        </div>
        <button class="sidebar-toggle" id="sidebarToggle">
            <span></span>
            <span></span>
            <span></span>
        </button>
    </div>
    
    <div class="sidebar-user">
        <div class="user-avatar">
            <img src="../assets/images/default-avatar.svg" alt="Admin Avatar">
        </div>
        <div class="user-info">
            <div class="user-name"><?php echo htmlspecialchars($user['name']); ?></div>
            <div class="user-role">Administrator</div>
        </div>
    </div>
    
    <nav class="sidebar-nav">
        <ul>
            <li class="nav-item <?php echo $currentPage === 'index.php' ? 'active' : ''; ?>">
                <a href="index.php">
                    <i>📊</i>
                    <span>Dashboard</span>
                </a>
            </li>
            
            <li class="nav-item <?php echo $currentPage === 'users.php' ? 'active' : ''; ?>">
                <a href="users.php">
                    <i>👥</i>
                    <span>Users</span>
                </a>
            </li>
            
            <li class="nav-item <?php echo $currentPage === 'stores.php' ? 'active' : ''; ?>">
                <a href="stores.php">
                    <i>🏪</i>
                    <span>Stores</span>
                </a>
            </li>
            
            <li class="nav-item <?php echo $currentPage === 'products.php' ? 'active' : ''; ?>">
                <a href="products.php">
                    <i>👕</i>
                    <span>Products</span>
                </a>
            </li>
            
            <li class="nav-item <?php echo $currentPage === 'orders.php' ? 'active' : ''; ?>">
                <a href="orders.php">
                    <i>📦</i>
                    <span>Orders</span>
                </a>
            </li>
            
            <li class="nav-divider"></li>
            
            <li class="nav-item <?php echo $currentPage === 'analytics.php' ? 'active' : ''; ?>">
                <a href="analytics.php">
                    <i>📈</i>
                    <span>Analytics</span>
                </a>
            </li>
            
            <li class="nav-item <?php echo $currentPage === 'reports.php' ? 'active' : ''; ?>">
                <a href="reports.php">
                    <i>📋</i>
                    <span>Reports</span>
                </a>
            </li>
            
            <li class="nav-divider"></li>
            
            <li class="nav-item <?php echo $currentPage === 'settings.php' ? 'active' : ''; ?>">
                <a href="settings.php">
                    <i>⚙️</i>
                    <span>Settings</span>
                </a>
            </li>
            
            <li class="nav-item <?php echo $currentPage === 'system.php' ? 'active' : ''; ?>">
                <a href="system.php">
                    <i>🔧</i>
                    <span>System</span>
                </a>
            </li>
            
            <li class="nav-divider"></li>
            
            <li class="nav-item">
                <a href="../dashboard/index.php">
                    <i>🏠</i>
                    <span>User Dashboard</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a href="../public-site/index.html" target="_blank">
                    <i>🌐</i>
                    <span>Public Site</span>
                </a>
            </li>
        </ul>
    </nav>
    
    <div class="sidebar-footer">
        <div class="theme-toggle">
            <button class="btn btn-outline theme-toggle-btn" onclick="toggleTheme()">
                <i class="theme-icon">🌙</i>
                <span>Dark Mode</span>
            </button>
        </div>
        
        <div class="admin-actions">
            <a href="../php/logout.php" class="btn btn-danger btn-sm btn-full">
                <i>🚪</i>
                <span>Logout</span>
            </a>
        </div>
    </div>
</aside>
