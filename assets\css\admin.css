/**
 * Admin Panel Styles
 * Multi-Tenant Print-on-Demand Platform
 */

/* Admin Statistics */
.admin-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

.stat-card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-500), var(--secondary-500));
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-4);
}

.stat-header h3 {
    margin: 0;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    background-color: var(--bg-secondary);
}

.stat-icon.users {
    background-color: var(--blue-100);
}

.stat-icon.stores {
    background-color: var(--green-100);
}

.stat-icon.products {
    background-color: var(--purple-100);
}

.stat-icon.orders {
    background-color: var(--orange-100);
}

.stat-icon.revenue {
    background-color: var(--yellow-100);
}

.stat-value {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--space-2);
    line-height: 1;
}

.stat-change {
    font-size: var(--font-size-sm);
    color: var(--text-tertiary);
    font-weight: var(--font-weight-medium);
}

/* Admin Actions */
.admin-actions {
    margin-bottom: var(--space-8);
}

.admin-actions h2 {
    margin: 0 0 var(--space-6) 0;
    color: var(--text-primary);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
}

/* Admin Sections */
.admin-sections {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-8);
    margin-bottom: var(--space-8);
}

.admin-section {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: var(--shadow-sm);
}

.admin-section.full-width {
    grid-column: 1 / -1;
}

/* Users List */
.users-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.user-item {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    padding: var(--space-4);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
}

.user-item:hover {
    background-color: var(--bg-secondary);
    border-color: var(--border-secondary);
}

.user-avatar {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-full);
    overflow: hidden;
    flex-shrink: 0;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-lg);
}

.user-details {
    flex: 1;
    min-width: 0;
}

.user-details h4 {
    margin: 0 0 var(--space-1) 0;
    color: var(--text-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
}

.user-details p {
    margin: 0 0 var(--space-1) 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.user-details small {
    color: var(--text-tertiary);
    font-size: var(--font-size-xs);
}

.user-actions {
    flex-shrink: 0;
}

/* Stores List */
.stores-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.store-item {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    padding: var(--space-4);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
}

.store-item:hover {
    background-color: var(--bg-secondary);
    border-color: var(--border-secondary);
}

.store-logo {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-lg);
    overflow: hidden;
    flex-shrink: 0;
    background-color: var(--bg-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
}

.store-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.logo-placeholder {
    font-size: 24px;
}

.store-details {
    flex: 1;
    min-width: 0;
}

.store-details h4 {
    margin: 0 0 var(--space-1) 0;
    color: var(--text-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
}

.store-details p {
    margin: 0 0 var(--space-1) 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.store-details small {
    color: var(--text-tertiary);
    font-size: var(--font-size-xs);
}

.store-status {
    flex-shrink: 0;
    margin-right: var(--space-3);
}

.store-actions {
    flex-shrink: 0;
}

/* Admin Tables */
.orders-table-container {
    overflow-x: auto;
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--bg-primary);
}

.admin-table th {
    background-color: var(--bg-secondary);
    padding: var(--space-4) var(--space-6);
    text-align: left;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 1px solid var(--border-primary);
}

.admin-table td {
    padding: var(--space-4) var(--space-6);
    border-bottom: 1px solid var(--border-primary);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.admin-table tr:hover {
    background-color: var(--bg-secondary);
}

.admin-table tr:last-child td {
    border-bottom: none;
}

/* Admin Sidebar Extensions */
.sidebar-footer {
    padding: var(--space-4) var(--space-6);
    border-top: 1px solid var(--border-primary);
}

.admin-actions {
    margin-top: var(--space-4);
}

.theme-toggle-btn {
    width: 100%;
    justify-content: flex-start;
    gap: var(--space-3);
}

/* Admin Modals */
.admin-modal .modal-content {
    max-width: 800px;
}

.admin-form {
    display: grid;
    gap: var(--space-4);
}

.form-section {
    background-color: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
}

.form-section h3 {
    margin: 0 0 var(--space-4) 0;
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    border-bottom: 1px solid var(--border-primary);
    padding-bottom: var(--space-2);
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-4);
}

.form-grid.single {
    grid-template-columns: 1fr;
}

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: var(--radius-full);
}

.status-dot.online {
    background-color: var(--green-500);
}

.status-dot.offline {
    background-color: var(--gray-400);
}

.status-dot.warning {
    background-color: var(--yellow-500);
}

.status-dot.error {
    background-color: var(--red-500);
}

/* Admin Alerts */
.admin-alert {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-left: 4px solid var(--color-primary);
    border-radius: var(--radius-lg);
    padding: var(--space-4) var(--space-6);
    margin-bottom: var(--space-6);
}

.admin-alert.warning {
    border-left-color: var(--yellow-500);
    background-color: var(--yellow-50);
}

.admin-alert.error {
    border-left-color: var(--red-500);
    background-color: var(--red-50);
}

.admin-alert.success {
    border-left-color: var(--green-500);
    background-color: var(--green-50);
}

.admin-alert h4 {
    margin: 0 0 var(--space-2) 0;
    color: var(--text-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
}

.admin-alert p {
    margin: 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .admin-stats {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .admin-sections {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .admin-stats {
        grid-template-columns: 1fr;
    }
    
    .stat-card {
        padding: var(--space-4);
    }
    
    .stat-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-3);
    }
    
    .user-item,
    .store-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-3);
    }
    
    .user-actions,
    .store-actions {
        width: 100%;
    }
    
    .user-actions .btn,
    .store-actions .btn {
        width: 100%;
        justify-content: center;
    }
    
    .orders-table-container {
        font-size: var(--font-size-xs);
    }
    
    .admin-table th,
    .admin-table td {
        padding: var(--space-3) var(--space-4);
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .stat-value {
        font-size: var(--font-size-2xl);
    }
    
    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 20px;
    }
    
    .user-avatar,
    .store-logo {
        width: 40px;
        height: 40px;
    }
    
    .avatar-placeholder {
        font-size: var(--font-size-base);
    }
}
