/**
 * Dashboard Styles
 * Multi-Tenant Print-on-Demand Platform
 */

/* Dashboard Layout */
.dashboard-layout {
    display: flex;
    min-height: 100vh;
    background-color: var(--bg-secondary);
}

/* Sidebar */
.dashboard-sidebar {
    width: 280px;
    background-color: var(--bg-primary);
    border-right: 1px solid var(--border-primary);
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    left: 0;
    top: 0;
    z-index: var(--z-fixed);
    transition: transform var(--transition-normal);
}

.dashboard-sidebar.collapsed {
    transform: translateX(-100%);
}

.sidebar-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-header .logo h2 {
    margin: 0;
    color: var(--color-primary);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
}

.sidebar-toggle {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--radius-md);
    transition: background-color var(--transition-fast);
}

.sidebar-toggle:hover {
    background-color: var(--bg-tertiary);
}

.sidebar-toggle span {
    display: block;
    width: 20px;
    height: 2px;
    background-color: var(--text-primary);
    margin: 3px 0;
    transition: var(--transition-fast);
}

/* Sidebar User */
.sidebar-user {
    padding: var(--space-4) var(--space-6);
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-full);
    overflow: hidden;
    flex-shrink: 0;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-info {
    flex: 1;
    min-width: 0;
}

.user-name {
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-role {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Sidebar Navigation */
.sidebar-nav {
    flex: 1;
    padding: var(--space-4) 0;
    overflow-y: auto;
}

.sidebar-nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin: 0 var(--space-3) var(--space-1) var(--space-3);
}

.nav-item a {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3) var(--space-4);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-fast);
}

.nav-item a:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    transform: translateX(2px);
}

.nav-item.active a {
    background-color: var(--primary-50);
    color: var(--color-primary);
    font-weight: var(--font-weight-semibold);
}

.nav-item a i {
    width: 20px;
    text-align: center;
    font-size: 16px;
}

.nav-divider {
    height: 1px;
    background-color: var(--border-primary);
    margin: var(--space-4) var(--space-6);
}

/* Sidebar Footer */
.sidebar-footer {
    padding: var(--space-4) var(--space-6);
    border-top: 1px solid var(--border-primary);
}

.theme-toggle {
    width: 100%;
}

.theme-toggle-btn {
    width: 100%;
    justify-content: flex-start;
}

/* Main Content */
.dashboard-main {
    flex: 1;
    margin-left: 280px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    transition: margin-left var(--transition-normal);
}

.dashboard-sidebar.collapsed + .dashboard-main {
    margin-left: 0;
}

/* Dashboard Header */
.dashboard-header {
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-primary);
    padding: var(--space-6) var(--space-8);
}

.dashboard-header h1 {
    margin: 0 0 var(--space-2) 0;
    color: var(--text-primary);
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
}

.dashboard-header p {
    margin: 0;
    color: var(--text-secondary);
    font-size: var(--font-size-base);
}

/* Dashboard Content */
.dashboard-content {
    flex: 1;
    padding: var(--space-8);
}

/* Dashboard Cards */
.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

.dashboard-card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

.dashboard-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.dashboard-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-4);
}

.dashboard-card-title {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin: 0;
}

.dashboard-card-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.dashboard-card-value {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin: 0 0 var(--space-2) 0;
}

.dashboard-card-change {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

.dashboard-card-change.positive {
    color: var(--color-success);
}

.dashboard-card-change.negative {
    color: var(--color-error);
}

/* Profile Container */
.profile-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--space-8);
}

.profile-card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    padding: var(--space-8);
    box-shadow: var(--shadow-sm);
}

.profile-header {
    display: flex;
    align-items: center;
    gap: var(--space-6);
    margin-bottom: var(--space-8);
    padding-bottom: var(--space-6);
    border-bottom: 1px solid var(--border-primary);
}

.profile-avatar {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: var(--radius-full);
    overflow: hidden;
    border: 3px solid var(--border-primary);
}

.profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-upload-btn {
    position: absolute;
    bottom: -5px;
    right: -5px;
    width: 30px;
    height: 30px;
    border-radius: var(--radius-full);
    background-color: var(--color-primary);
    color: white;
    border: 2px solid var(--bg-primary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all var(--transition-fast);
}

.avatar-upload-btn:hover {
    background-color: var(--primary-600);
    transform: scale(1.1);
}

.profile-info h2 {
    margin: 0 0 var(--space-1) 0;
    color: var(--text-primary);
    font-size: var(--font-size-2xl);
}

.profile-info p {
    margin: 0 0 var(--space-2) 0;
    color: var(--text-secondary);
}

.profile-badge {
    display: inline-block;
    padding: var(--space-1) var(--space-3);
    background-color: var(--primary-100);
    color: var(--primary-600);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Form Sections */
.form-section {
    margin-bottom: var(--space-8);
}

.form-section h3 {
    margin: 0 0 var(--space-4) 0;
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    padding-bottom: var(--space-2);
    border-bottom: 1px solid var(--border-primary);
}

.form-actions {
    display: flex;
    gap: var(--space-4);
    padding-top: var(--space-6);
    border-top: 1px solid var(--border-primary);
}

/* Profile Stats */
.profile-stats {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: var(--shadow-sm);
}

.profile-stats h3 {
    margin: 0 0 var(--space-6) 0;
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
}

.stats-grid {
    display: grid;
    gap: var(--space-4);
}

.stat-item {
    text-align: center;
    padding: var(--space-4);
    border-radius: var(--radius-lg);
    background-color: var(--bg-secondary);
}

.stat-value {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-primary);
    margin-bottom: var(--space-1);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .profile-container {
        grid-template-columns: 1fr;
    }
    
    .dashboard-cards {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .dashboard-sidebar {
        transform: translateX(-100%);
    }
    
    .dashboard-sidebar.show {
        transform: translateX(0);
    }
    
    .dashboard-main {
        margin-left: 0;
    }
    
    .sidebar-toggle {
        display: block;
    }
    
    .dashboard-header {
        padding: var(--space-4) var(--space-4);
    }
    
    .dashboard-content {
        padding: var(--space-4);
    }
    
    .dashboard-cards {
        grid-template-columns: 1fr;
    }
    
    .profile-header {
        flex-direction: column;
        text-align: center;
    }
    
    .form-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .dashboard-header h1 {
        font-size: var(--font-size-2xl);
    }
    
    .profile-card {
        padding: var(--space-4);
    }
    
    .profile-avatar {
        width: 60px;
        height: 60px;
    }
}
