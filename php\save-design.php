<?php
/**
 * Save Design Handler
 * Multi-Tenant Print-on-Demand Platform
 */

require_once '../config/config.php';

// Set JSON response header
header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'User not authenticated']);
    exit;
}

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

try {
    $user = getCurrentUser();
    if (!$user) {
        echo json_encode(['success' => false, 'message' => 'User not found']);
        exit;
    }
    
    // Get user's store
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM stores WHERE user_id = ? LIMIT 1");
    $stmt->execute([$user['id']]);
    $store = $stmt->fetch();
    
    if (!$store) {
        echo json_encode(['success' => false, 'message' => 'Store not found']);
        exit;
    }
    
    // Validate input data
    $productName = sanitizeInput($_POST['productName'] ?? '');
    $productDescription = sanitizeInput($_POST['productDescription'] ?? '');
    $productPrice = floatval($_POST['productPrice'] ?? 0);
    $productTags = sanitizeInput($_POST['productTags'] ?? '');
    $tshirtColor = sanitizeInput($_POST['tshirtColor'] ?? '#FFFFFF');
    $selectedSizes = json_decode($_POST['selectedSizes'] ?? '[]', true);
    $designData = $_POST['designData'] ?? '';
    $designImage = $_POST['designImage'] ?? '';
    
    // Validation
    if (empty($productName)) {
        echo json_encode(['success' => false, 'message' => 'Product name is required']);
        exit;
    }
    
    if ($productPrice <= 0) {
        echo json_encode(['success' => false, 'message' => 'Product price must be greater than 0']);
        exit;
    }
    
    if (empty($selectedSizes) || !is_array($selectedSizes)) {
        echo json_encode(['success' => false, 'message' => 'At least one size must be selected']);
        exit;
    }
    
    if (empty($designData)) {
        echo json_encode(['success' => false, 'message' => 'Design data is required']);
        exit;
    }
    
    if (empty($designImage)) {
        echo json_encode(['success' => false, 'message' => 'Design image is required']);
        exit;
    }
    
    // Process design image
    $designImagePath = saveDesignImage($designImage, $user['id']);
    if (!$designImagePath) {
        echo json_encode(['success' => false, 'message' => 'Failed to save design image']);
        exit;
    }
    
    // Generate mockup image
    $mockupImagePath = generateMockupImage($designImage, $tshirtColor, $user['id']);
    if (!$mockupImagePath) {
        echo json_encode(['success' => false, 'message' => 'Failed to generate mockup image']);
        exit;
    }
    
    // Prepare colors array
    $colors = [];
    foreach (TSHIRT_COLORS as $color) {
        if ($color['hex'] === $tshirtColor) {
            $colors[] = $color;
            break;
        }
    }
    
    // If color not found in predefined colors, add it
    if (empty($colors)) {
        $colors[] = ['name' => 'Custom', 'hex' => $tshirtColor];
    }
    
    // Save product to database
    $stmt = $db->prepare("
        INSERT INTO products (
            store_id, name, description, design_path, mockup_path, 
            base_price, sizes, colors, design_config, tags, is_active
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
    ");
    
    $result = $stmt->execute([
        $store['id'],
        $productName,
        $productDescription,
        $designImagePath,
        $mockupImagePath,
        $productPrice,
        json_encode($selectedSizes),
        json_encode($colors),
        $designData,
        $productTags
    ]);
    
    if ($result) {
        $productId = $db->lastInsertId();
        
        // Update store product count
        $stmt = $db->prepare("UPDATE stores SET total_products = total_products + 1 WHERE id = ?");
        $stmt->execute([$store['id']]);
        
        echo json_encode([
            'success' => true, 
            'message' => 'Product saved successfully',
            'product_id' => $productId
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to save product to database']);
    }
    
} catch (Exception $e) {
    error_log("Save design error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while saving the design']);
}

/**
 * Save design image from base64 data
 */
function saveDesignImage($base64Data, $userId) {
    try {
        // Remove data URL prefix
        $imageData = preg_replace('#^data:image/[^;]*;base64,#', '', $base64Data);
        $imageData = base64_decode($imageData);
        
        if ($imageData === false) {
            return false;
        }
        
        // Generate unique filename
        $filename = 'design_' . $userId . '_' . time() . '_' . uniqid() . '.png';
        $filepath = UPLOAD_DESIGNS_PATH . '/' . $filename;
        
        // Save file
        if (file_put_contents($filepath, $imageData)) {
            return $filename;
        }
        
        return false;
    } catch (Exception $e) {
        error_log("Save design image error: " . $e->getMessage());
        return false;
    }
}

/**
 * Generate mockup image with t-shirt background
 */
function generateMockupImage($designBase64, $tshirtColor, $userId) {
    try {
        // For now, we'll just save the design image as mockup
        // In a real implementation, you would composite the design onto a t-shirt template
        
        $imageData = preg_replace('#^data:image/[^;]*;base64,#', '', $designBase64);
        $imageData = base64_decode($imageData);
        
        if ($imageData === false) {
            return false;
        }
        
        // Create image from string
        $designImage = imagecreatefromstring($imageData);
        if (!$designImage) {
            return false;
        }
        
        // Create t-shirt mockup canvas
        $mockupWidth = 600;
        $mockupHeight = 700;
        $mockup = imagecreatetruecolor($mockupWidth, $mockupHeight);
        
        // Set t-shirt background color
        $rgb = hexToRgb($tshirtColor);
        $bgColor = imagecolorallocate($mockup, $rgb['r'], $rgb['g'], $rgb['b']);
        imagefill($mockup, 0, 0, $bgColor);
        
        // Get design dimensions
        $designWidth = imagesx($designImage);
        $designHeight = imagesy($designImage);
        
        // Calculate position to center design on t-shirt
        $designX = ($mockupWidth - $designWidth) / 2;
        $designY = ($mockupHeight - $designHeight) / 2;
        
        // Copy design onto mockup
        imagecopy($mockup, $designImage, $designX, $designY, 0, 0, $designWidth, $designHeight);
        
        // Save mockup
        $filename = 'mockup_' . $userId . '_' . time() . '_' . uniqid() . '.png';
        $filepath = UPLOAD_MOCKUPS_PATH . '/' . $filename;
        
        if (imagepng($mockup, $filepath)) {
            // Clean up
            imagedestroy($designImage);
            imagedestroy($mockup);
            return $filename;
        }
        
        // Clean up on failure
        imagedestroy($designImage);
        imagedestroy($mockup);
        return false;
        
    } catch (Exception $e) {
        error_log("Generate mockup error: " . $e->getMessage());
        return false;
    }
}

/**
 * Convert hex color to RGB
 */
function hexToRgb($hex) {
    $hex = ltrim($hex, '#');
    
    if (strlen($hex) == 3) {
        $hex = $hex[0] . $hex[0] . $hex[1] . $hex[1] . $hex[2] . $hex[2];
    }
    
    return [
        'r' => hexdec(substr($hex, 0, 2)),
        'g' => hexdec(substr($hex, 2, 2)),
        'b' => hexdec(substr($hex, 4, 2))
    ];
}
?>
