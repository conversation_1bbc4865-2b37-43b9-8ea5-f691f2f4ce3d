/**
 * Landing Page Styles
 * Multi-Tenant Print-on-Demand Platform
 */

/* Landing Page Layout */
.landing-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: var(--space-8);
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--secondary-500) 100%);
    color: white;
}

.landing-container h1 {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--space-6);
    color: white;
}

.landing-container p {
    font-size: var(--font-size-xl);
    margin-bottom: var(--space-8);
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
}

.cta-buttons {
    display: flex;
    gap: var(--space-4);
    flex-wrap: wrap;
    justify-content: center;
}

.cta-buttons .btn {
    padding: var(--space-4) var(--space-8);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    border-radius: var(--radius-xl);
    text-decoration: none;
    transition: all var(--transition-normal);
}

.cta-buttons .btn-primary {
    background-color: white;
    color: var(--primary-500);
    border: 2px solid white;
}

.cta-buttons .btn-primary:hover {
    background-color: transparent;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.cta-buttons .btn-secondary {
    background-color: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.5);
}

.cta-buttons .btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: white;
    transform: translateY(-2px);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 100%);
    color: white;
    padding: var(--space-20) 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 1;
    max-width: 800px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.hero h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--space-6);
    line-height: 1.1;
}

.hero p {
    font-size: var(--font-size-xl);
    margin-bottom: var(--space-8);
    opacity: 0.9;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: var(--space-4);
    justify-content: center;
    flex-wrap: wrap;
}

/* Features Section */
.features {
    padding: var(--space-20) 0;
    background-color: var(--bg-secondary);
}

.features-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.features-header {
    text-align: center;
    margin-bottom: var(--space-16);
}

.features-header h2 {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--space-4);
    color: var(--text-primary);
}

.features-header p {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-8);
}

.feature-card {
    background-color: var(--bg-primary);
    padding: var(--space-8);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-sm);
    text-align: center;
    transition: all var(--transition-normal);
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--space-6) auto;
    background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
    border-radius: var(--radius-2xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
}

.feature-card h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-4);
    color: var(--text-primary);
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* How It Works Section */
.how-it-works {
    padding: var(--space-20) 0;
    background-color: var(--bg-primary);
}

.section-header {
    text-align: center;
    margin-bottom: var(--space-16);
}

.section-header h2 {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--space-4);
    color: var(--text-primary);
}

.section-header p {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

.steps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-8);
    max-width: 1000px;
    margin: 0 auto;
}

.step-card {
    text-align: center;
    padding: var(--space-8);
    position: relative;
}

.step-number {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
    color: white;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    margin: 0 auto var(--space-6) auto;
    position: relative;
    z-index: 1;
}

.step-card h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-4);
    color: var(--text-primary);
}

.step-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Add connecting lines between steps on larger screens */
@media (min-width: 768px) {
    .step-card:not(:last-child)::after {
        content: '';
        position: absolute;
        top: 40px;
        right: -50%;
        width: 100%;
        height: 2px;
        background: linear-gradient(90deg, var(--primary-500), var(--secondary-500));
        z-index: 0;
    }

    .steps-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--secondary-500) 100%);
    color: white;
    padding: var(--space-20) 0;
    text-align: center;
}

.cta-content {
    max-width: 600px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.cta-section h2 {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--space-4);
    color: white;
}

.cta-section p {
    font-size: var(--font-size-lg);
    margin-bottom: var(--space-8);
    opacity: 0.9;
}

/* Footer */
.footer {
    background-color: var(--gray-900);
    color: var(--gray-300);
    padding: var(--space-12) 0 var(--space-6) 0;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-8);
}

.footer-section h3 {
    color: white;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-4);
}

.footer-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-section li {
    margin-bottom: var(--space-2);
}

.footer-section a {
    color: var(--gray-400);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-section a:hover {
    color: white;
}

.footer-bottom {
    border-top: 1px solid var(--gray-700);
    margin-top: var(--space-8);
    padding-top: var(--space-6);
    text-align: center;
    color: var(--gray-500);
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-primary);
    z-index: var(--z-fixed);
    transition: all var(--transition-normal);
}

.navbar.scrolled {
    background-color: var(--bg-primary);
    box-shadow: var(--shadow-sm);
}

.navbar-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

.navbar-brand {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-primary);
    text-decoration: none;
}

.navbar-nav {
    display: flex;
    align-items: center;
    gap: var(--space-6);
    list-style: none;
    margin: 0;
    padding: 0;
}

.navbar-nav a {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    transition: color var(--transition-fast);
}

.navbar-nav a:hover {
    color: var(--color-primary);
}

.navbar-toggle {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--space-2);
}

.navbar-toggle span {
    display: block;
    width: 25px;
    height: 3px;
    background-color: var(--text-primary);
    margin: 5px 0;
    transition: var(--transition-fast);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero {
        padding: var(--space-16) 0;
    }
    
    .hero h1 {
        font-size: var(--font-size-3xl);
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .features {
        padding: var(--space-12) 0;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }
    
    .cta-section {
        padding: var(--space-12) 0;
    }
    
    .navbar-nav {
        display: none;
    }
    
    .navbar-toggle {
        display: block;
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .landing-container {
        padding: var(--space-4);
    }
    
    .landing-container h1 {
        font-size: var(--font-size-3xl);
    }
}

@media (max-width: 480px) {
    .hero h1 {
        font-size: var(--font-size-2xl);
    }
    
    .hero p {
        font-size: var(--font-size-base);
    }
    
    .feature-card {
        padding: var(--space-6);
    }
    
    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}
