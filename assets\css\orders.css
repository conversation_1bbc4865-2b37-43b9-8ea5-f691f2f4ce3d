/**
 * Orders Page Styles
 * Multi-Tenant Print-on-Demand Platform
 */

/* Order Statistics */
.order-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-4);
    margin-bottom: var(--space-8);
}

.stat-card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    text-align: center;
    transition: all var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-value {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-primary);
    margin-bottom: var(--space-2);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: var(--font-weight-medium);
}

/* Orders Table */
.orders-table-container {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.orders-table {
    width: 100%;
    border-collapse: collapse;
}

.orders-table th {
    background-color: var(--bg-secondary);
    padding: var(--space-4) var(--space-6);
    text-align: left;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 1px solid var(--border-primary);
}

.orders-table td {
    padding: var(--space-4) var(--space-6);
    border-bottom: 1px solid var(--border-primary);
    vertical-align: middle;
}

.order-row {
    transition: background-color var(--transition-fast);
}

.order-row:hover {
    background-color: var(--bg-secondary);
}

.order-row:last-child td {
    border-bottom: none;
}

/* Order Info */
.order-info strong {
    display: block;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-1);
}

.order-info small {
    color: var(--text-tertiary);
    font-size: var(--font-size-xs);
}

/* Product Info */
.product-info {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.product-thumbnail {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: var(--radius-md);
    border: 1px solid var(--border-primary);
    flex-shrink: 0;
}

.product-info strong {
    display: block;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--space-1);
    line-height: 1.3;
}

.product-info small {
    color: var(--text-tertiary);
    font-size: var(--font-size-xs);
}

/* Customer Info */
.customer-info strong {
    display: block;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--space-1);
}

.customer-info small {
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
}

/* Date Info */
.date-info strong {
    display: block;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    margin-bottom: var(--space-1);
}

.date-info small {
    color: var(--text-tertiary);
    font-size: var(--font-size-xs);
}

/* Order Actions */
.order-actions {
    display: flex;
    gap: var(--space-2);
}

.action-btn {
    width: 32px;
    height: 32px;
    border: 1px solid var(--border-primary);
    background-color: var(--bg-secondary);
    border-radius: var(--radius-md);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    transition: all var(--transition-fast);
}

.action-btn:hover {
    background-color: var(--bg-tertiary);
    border-color: var(--border-secondary);
    transform: scale(1.05);
}

/* Empty Actions */
.empty-actions {
    display: flex;
    gap: var(--space-4);
    justify-content: center;
    margin-top: var(--space-6);
}

/* Order Detail Modal */
.order-detail-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-6);
}

.order-detail-section {
    background-color: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
}

.order-detail-section h4 {
    margin: 0 0 var(--space-3) 0;
    color: var(--text-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    border-bottom: 1px solid var(--border-primary);
    padding-bottom: var(--space-2);
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-2) 0;
    border-bottom: 1px solid var(--border-primary);
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
}

.detail-value {
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    font-weight: var(--font-weight-medium);
}

.order-product-preview {
    text-align: center;
    margin-bottom: var(--space-4);
}

.order-product-preview img {
    width: 150px;
    height: 150px;
    object-fit: cover;
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
}

/* Status Update Form */
.status-update-form {
    margin-top: var(--space-4);
}

.status-select {
    width: 100%;
    padding: var(--space-3);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.status-note {
    width: 100%;
    min-height: 80px;
    margin-top: var(--space-3);
    padding: var(--space-3);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    resize: vertical;
}

/* Order Timeline */
.order-timeline {
    margin-top: var(--space-4);
}

.timeline-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3) 0;
    border-bottom: 1px solid var(--border-primary);
}

.timeline-item:last-child {
    border-bottom: none;
}

.timeline-icon {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-full);
    background-color: var(--primary-100);
    color: var(--primary-600);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    flex-shrink: 0;
}

.timeline-content {
    flex: 1;
}

.timeline-title {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    margin-bottom: var(--space-1);
}

.timeline-date {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .orders-table-container {
        overflow-x: auto;
    }
    
    .orders-table {
        min-width: 800px;
    }
    
    .order-detail-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .order-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .orders-table th,
    .orders-table td {
        padding: var(--space-3) var(--space-4);
    }
    
    .product-info {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-2);
    }
    
    .product-thumbnail {
        width: 40px;
        height: 40px;
    }
    
    .order-actions {
        flex-direction: column;
        gap: var(--space-1);
    }
    
    .action-btn {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }
    
    .empty-actions {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 480px) {
    .order-stats {
        grid-template-columns: 1fr;
    }
    
    .orders-table {
        min-width: 600px;
    }
    
    .orders-table th,
    .orders-table td {
        padding: var(--space-2) var(--space-3);
        font-size: var(--font-size-xs);
    }
    
    .stat-value {
        font-size: var(--font-size-2xl);
    }
    
    .order-product-preview img {
        width: 120px;
        height: 120px;
    }
}
