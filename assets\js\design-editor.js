/**
 * Design Editor JavaScript
 * Multi-Tenant Print-on-Demand Platform
 */

// Global variables
let canvas;
let previewCanvas;
let currentTshirtColor = '#FFFFFF';
let selectedSizes = ['S', 'M', 'L', 'XL', 'XXL'];
let undoStack = [];
let redoStack = [];
let zoomLevel = 1;

// Initialize design editor
document.addEventListener('DOMContentLoaded', function() {
    initCanvas();
    initEventListeners();
    initUploadArea();
    initColorPickers();
    initTshirtOptions();
    updateLayersList();
});

/**
 * Initialize Fabric.js canvas
 */
function initCanvas() {
    // Main design canvas
    canvas = new fabric.Canvas('designCanvas', {
        width: 400,
        height: 500,
        backgroundColor: 'transparent',
        selection: true,
        preserveObjectStacking: true
    });
    
    // Preview canvas
    previewCanvas = new fabric.Canvas('previewCanvas', {
        width: 400,
        height: 500,
        backgroundColor: 'transparent',
        selection: false,
        interactive: false
    });
    
    // Canvas event listeners
    canvas.on('selection:created', handleObjectSelection);
    canvas.on('selection:updated', handleObjectSelection);
    canvas.on('selection:cleared', handleObjectDeselection);
    canvas.on('object:modified', saveCanvasState);
    canvas.on('object:added', function() {
        updateLayersList();
        saveCanvasState();
    });
    canvas.on('object:removed', function() {
        updateLayersList();
        saveCanvasState();
    });
    
    // Initial canvas state
    saveCanvasState();
}

/**
 * Initialize event listeners
 */
function initEventListeners() {
    // Tool buttons
    document.getElementById('addText').addEventListener('click', addTextToCanvas);
    document.getElementById('deleteSelected').addEventListener('click', deleteSelectedObject);
    document.getElementById('clearCanvas').addEventListener('click', clearCanvas);
    document.getElementById('undoAction').addEventListener('click', undoLastAction);
    document.getElementById('redoAction').addEventListener('click', redoLastAction);
    
    // Zoom controls
    document.getElementById('zoomIn').addEventListener('click', () => zoomCanvas(1.1));
    document.getElementById('zoomOut').addEventListener('click', () => zoomCanvas(0.9));
    document.getElementById('resetView').addEventListener('click', resetCanvasView);
    
    // Text controls
    document.getElementById('textInput').addEventListener('input', updateSelectedText);
    document.getElementById('fontFamily').addEventListener('change', updateSelectedTextFont);
    document.getElementById('fontSize').addEventListener('input', updateSelectedTextSize);
    
    // Color picker
    document.getElementById('colorPicker').addEventListener('change', updateSelectedObjectColor);
    
    // Color presets
    document.querySelectorAll('.color-preset').forEach(preset => {
        preset.addEventListener('click', function() {
            const color = this.getAttribute('data-color');
            document.getElementById('colorPicker').value = color;
            updateSelectedObjectColor();
            updateColorPresetSelection(this);
        });
    });
    
    // Save and preview buttons
    document.getElementById('saveDesign').addEventListener('click', openSaveModal);
    document.getElementById('previewDesign').addEventListener('click', openPreviewModal);
    document.getElementById('saveFromPreview').addEventListener('click', openSaveModal);
    
    // Save form
    document.getElementById('saveDesignForm').addEventListener('submit', saveDesignAsProduct);
    
    // Font size slider
    const fontSizeSlider = document.getElementById('fontSize');
    const fontSizeValue = document.getElementById('fontSizeValue');
    fontSizeSlider.addEventListener('input', function() {
        fontSizeValue.textContent = this.value;
        updateSelectedTextSize();
    });
}

/**
 * Initialize upload area
 */
function initUploadArea() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('designUpload');
    
    uploadArea.addEventListener('click', () => fileInput.click());
    
    // Drag and drop
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileUpload(files[0]);
        }
    });
    
    fileInput.addEventListener('change', function() {
        if (this.files.length > 0) {
            handleFileUpload(this.files[0]);
        }
    });
}

/**
 * Handle file upload
 */
function handleFileUpload(file) {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
        showNotification('Please upload a valid image file (JPG, PNG, GIF)', 'error');
        return;
    }
    
    // Validate file size (5MB)
    if (file.size > 5 * 1024 * 1024) {
        showNotification('File size must be less than 5MB', 'error');
        return;
    }
    
    const reader = new FileReader();
    reader.onload = function(e) {
        fabric.Image.fromURL(e.target.result, function(img) {
            // Scale image to fit canvas
            const maxWidth = canvas.width * 0.8;
            const maxHeight = canvas.height * 0.8;
            
            if (img.width > maxWidth || img.height > maxHeight) {
                const scale = Math.min(maxWidth / img.width, maxHeight / img.height);
                img.scale(scale);
            }
            
            // Center the image
            img.set({
                left: canvas.width / 2,
                top: canvas.height / 2,
                originX: 'center',
                originY: 'center'
            });
            
            canvas.add(img);
            canvas.setActiveObject(img);
            canvas.renderAll();
            
            showNotification('Image uploaded successfully', 'success');
        });
    };
    
    reader.readAsDataURL(file);
}

/**
 * Initialize color pickers
 */
function initColorPickers() {
    // Set initial color preset selection
    const firstPreset = document.querySelector('.color-preset[data-color="#000000"]');
    if (firstPreset) {
        updateColorPresetSelection(firstPreset);
    }
}

/**
 * Update color preset selection
 */
function updateColorPresetSelection(selectedPreset) {
    document.querySelectorAll('.color-preset').forEach(preset => {
        preset.classList.remove('active');
    });
    selectedPreset.classList.add('active');
}

/**
 * Initialize t-shirt options
 */
function initTshirtOptions() {
    // T-shirt color options
    document.querySelectorAll('.tshirt-color-option').forEach(option => {
        option.addEventListener('click', function() {
            // Update selection
            document.querySelectorAll('.tshirt-color-option').forEach(opt => {
                opt.classList.remove('active');
            });
            this.classList.add('active');
            
            // Update t-shirt color
            currentTshirtColor = this.getAttribute('data-color');
            updateTshirtBackground();
        });
    });
    
    // Size checkboxes
    document.querySelectorAll('input[name="sizes[]"]').forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedSizes);
    });
    
    updateSelectedSizes();
}

/**
 * Update t-shirt background color
 */
function updateTshirtBackground() {
    const mockup = document.querySelector('.tshirt-mockup');
    if (mockup) {
        // Update SVG background color
        const svgData = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 500"><path d="M50 100 L50 50 Q50 30 70 30 L130 30 Q140 20 160 20 L240 20 Q260 20 270 30 L330 30 Q350 30 350 50 L350 100 L380 120 L380 180 L350 160 L350 450 Q350 470 330 470 L70 470 Q50 470 50 450 L50 160 L20 180 L20 120 Z" fill="${currentTshirtColor}" stroke="%23e5e7eb" stroke-width="2"/></svg>`;
        const encodedSvg = encodeURIComponent(svgData);
        mockup.style.backgroundImage = `url("data:image/svg+xml,${encodedSvg}")`;
    }
}

/**
 * Update selected sizes
 */
function updateSelectedSizes() {
    selectedSizes = [];
    document.querySelectorAll('input[name="sizes[]"]:checked').forEach(checkbox => {
        selectedSizes.push(checkbox.value);
    });
}

/**
 * Add text to canvas
 */
function addTextToCanvas() {
    const text = new fabric.IText('Your Text Here', {
        left: canvas.width / 2,
        top: canvas.height / 2,
        originX: 'center',
        originY: 'center',
        fontFamily: 'Arial',
        fontSize: 30,
        fill: '#000000'
    });
    
    canvas.add(text);
    canvas.setActiveObject(text);
    canvas.renderAll();
    
    // Show text controls
    document.getElementById('textControls').style.display = 'block';
    document.getElementById('textInput').value = text.text;
}

/**
 * Handle object selection
 */
function handleObjectSelection(e) {
    const activeObject = canvas.getActiveObject();
    if (!activeObject) return;
    
    updateObjectProperties(activeObject);
    
    // Show relevant controls
    if (activeObject.type === 'i-text' || activeObject.type === 'text') {
        document.getElementById('textControls').style.display = 'block';
        document.getElementById('textInput').value = activeObject.text;
        document.getElementById('fontFamily').value = activeObject.fontFamily;
        document.getElementById('fontSize').value = activeObject.fontSize;
        document.getElementById('fontSizeValue').textContent = activeObject.fontSize;
    }
    
    // Update color picker
    if (activeObject.fill) {
        document.getElementById('colorPicker').value = activeObject.fill;
    }
}

/**
 * Handle object deselection
 */
function handleObjectDeselection() {
    document.getElementById('textControls').style.display = 'none';
    clearObjectProperties();
}

/**
 * Update selected text
 */
function updateSelectedText() {
    const activeObject = canvas.getActiveObject();
    if (activeObject && (activeObject.type === 'i-text' || activeObject.type === 'text')) {
        activeObject.set('text', document.getElementById('textInput').value);
        canvas.renderAll();
    }
}

/**
 * Update selected text font
 */
function updateSelectedTextFont() {
    const activeObject = canvas.getActiveObject();
    if (activeObject && (activeObject.type === 'i-text' || activeObject.type === 'text')) {
        activeObject.set('fontFamily', document.getElementById('fontFamily').value);
        canvas.renderAll();
    }
}

/**
 * Update selected text size
 */
function updateSelectedTextSize() {
    const activeObject = canvas.getActiveObject();
    if (activeObject && (activeObject.type === 'i-text' || activeObject.type === 'text')) {
        activeObject.set('fontSize', parseInt(document.getElementById('fontSize').value));
        canvas.renderAll();
    }
}

/**
 * Update selected object color
 */
function updateSelectedObjectColor() {
    const activeObject = canvas.getActiveObject();
    const color = document.getElementById('colorPicker').value;
    
    if (activeObject) {
        if (activeObject.type === 'i-text' || activeObject.type === 'text') {
            activeObject.set('fill', color);
        } else if (activeObject.type === 'image') {
            // For images, we could apply filters or tint
            activeObject.set('fill', color);
        } else {
            activeObject.set('fill', color);
        }
        canvas.renderAll();
    }
}

/**
 * Delete selected object
 */
function deleteSelectedObject() {
    const activeObject = canvas.getActiveObject();
    if (activeObject) {
        canvas.remove(activeObject);
        canvas.renderAll();
    }
}

/**
 * Clear canvas
 */
function clearCanvas() {
    if (confirm('Are you sure you want to clear all objects from the canvas?')) {
        canvas.clear();
        canvas.renderAll();
        updateLayersList();
    }
}

/**
 * Save canvas state for undo/redo
 */
function saveCanvasState() {
    const state = JSON.stringify(canvas.toJSON());
    undoStack.push(state);
    
    // Limit undo stack size
    if (undoStack.length > 20) {
        undoStack.shift();
    }
    
    // Clear redo stack when new action is performed
    redoStack = [];
}

/**
 * Undo last action
 */
function undoLastAction() {
    if (undoStack.length > 1) {
        const currentState = undoStack.pop();
        redoStack.push(currentState);
        
        const previousState = undoStack[undoStack.length - 1];
        canvas.loadFromJSON(previousState, function() {
            canvas.renderAll();
            updateLayersList();
        });
    }
}

/**
 * Redo last action
 */
function redoLastAction() {
    if (redoStack.length > 0) {
        const state = redoStack.pop();
        undoStack.push(state);
        
        canvas.loadFromJSON(state, function() {
            canvas.renderAll();
            updateLayersList();
        });
    }
}

/**
 * Zoom canvas
 */
function zoomCanvas(factor) {
    zoomLevel *= factor;
    zoomLevel = Math.max(0.1, Math.min(3, zoomLevel)); // Limit zoom between 10% and 300%
    
    canvas.setZoom(zoomLevel);
    canvas.renderAll();
    
    document.getElementById('zoomLevel').textContent = Math.round(zoomLevel * 100) + '%';
}

/**
 * Reset canvas view
 */
function resetCanvasView() {
    zoomLevel = 1;
    canvas.setZoom(1);
    canvas.viewportTransform = [1, 0, 0, 1, 0, 0];
    canvas.renderAll();

    document.getElementById('zoomLevel').textContent = '100%';
}

/**
 * Update object properties panel
 */
function updateObjectProperties(obj) {
    const propertiesPanel = document.getElementById('objectProperties');

    let html = '<div class="property-group">';
    html += `<label>Object Type</label>`;
    html += `<input type="text" value="${obj.type}" readonly class="form-control">`;
    html += '</div>';

    if (obj.type === 'i-text' || obj.type === 'text') {
        html += '<div class="property-group">';
        html += '<label>Text</label>';
        html += `<input type="text" id="propText" value="${obj.text}" class="form-control">`;
        html += '</div>';

        html += '<div class="property-group">';
        html += '<label>Font Size</label>';
        html += `<input type="number" id="propFontSize" value="${obj.fontSize}" min="8" max="200" class="form-control">`;
        html += '</div>';
    }

    html += '<div class="property-group">';
    html += '<label>X Position</label>';
    html += `<input type="number" id="propLeft" value="${Math.round(obj.left)}" class="form-control">`;
    html += '</div>';

    html += '<div class="property-group">';
    html += '<label>Y Position</label>';
    html += `<input type="number" id="propTop" value="${Math.round(obj.top)}" class="form-control">`;
    html += '</div>';

    html += '<div class="property-group">';
    html += '<label>Width</label>';
    html += `<input type="number" id="propWidth" value="${Math.round(obj.width * obj.scaleX)}" class="form-control">`;
    html += '</div>';

    html += '<div class="property-group">';
    html += '<label>Height</label>';
    html += `<input type="number" id="propHeight" value="${Math.round(obj.height * obj.scaleY)}" class="form-control">`;
    html += '</div>';

    html += '<div class="property-group">';
    html += '<label>Rotation</label>';
    html += `<input type="number" id="propAngle" value="${Math.round(obj.angle)}" min="0" max="360" class="form-control">`;
    html += '</div>';

    html += '<div class="property-group">';
    html += '<label>Opacity</label>';
    html += `<input type="range" id="propOpacity" value="${obj.opacity}" min="0" max="1" step="0.1" class="range-slider">`;
    html += `<span>${Math.round(obj.opacity * 100)}%</span>`;
    html += '</div>';

    propertiesPanel.innerHTML = html;

    // Add event listeners for property changes
    addPropertyEventListeners();
}

/**
 * Clear object properties panel
 */
function clearObjectProperties() {
    const propertiesPanel = document.getElementById('objectProperties');
    propertiesPanel.innerHTML = '<p class="no-selection">Select an object to edit properties</p>';
}

/**
 * Add event listeners for property inputs
 */
function addPropertyEventListeners() {
    const activeObject = canvas.getActiveObject();
    if (!activeObject) return;

    // Text property
    const propText = document.getElementById('propText');
    if (propText) {
        propText.addEventListener('input', function() {
            activeObject.set('text', this.value);
            canvas.renderAll();
        });
    }

    // Font size property
    const propFontSize = document.getElementById('propFontSize');
    if (propFontSize) {
        propFontSize.addEventListener('input', function() {
            activeObject.set('fontSize', parseInt(this.value));
            canvas.renderAll();
        });
    }

    // Position properties
    const propLeft = document.getElementById('propLeft');
    if (propLeft) {
        propLeft.addEventListener('input', function() {
            activeObject.set('left', parseInt(this.value));
            canvas.renderAll();
        });
    }

    const propTop = document.getElementById('propTop');
    if (propTop) {
        propTop.addEventListener('input', function() {
            activeObject.set('top', parseInt(this.value));
            canvas.renderAll();
        });
    }

    // Size properties
    const propWidth = document.getElementById('propWidth');
    if (propWidth) {
        propWidth.addEventListener('input', function() {
            const newWidth = parseInt(this.value);
            const scale = newWidth / activeObject.width;
            activeObject.set('scaleX', scale);
            canvas.renderAll();
        });
    }

    const propHeight = document.getElementById('propHeight');
    if (propHeight) {
        propHeight.addEventListener('input', function() {
            const newHeight = parseInt(this.value);
            const scale = newHeight / activeObject.height;
            activeObject.set('scaleY', scale);
            canvas.renderAll();
        });
    }

    // Rotation property
    const propAngle = document.getElementById('propAngle');
    if (propAngle) {
        propAngle.addEventListener('input', function() {
            activeObject.set('angle', parseInt(this.value));
            canvas.renderAll();
        });
    }

    // Opacity property
    const propOpacity = document.getElementById('propOpacity');
    if (propOpacity) {
        propOpacity.addEventListener('input', function() {
            activeObject.set('opacity', parseFloat(this.value));
            canvas.renderAll();
            this.nextElementSibling.textContent = Math.round(this.value * 100) + '%';
        });
    }
}

/**
 * Update layers list
 */
function updateLayersList() {
    const layersList = document.getElementById('layersList');
    const objects = canvas.getObjects();

    if (objects.length === 0) {
        layersList.innerHTML = '<p class="no-selection">No layers</p>';
        return;
    }

    let html = '';
    objects.forEach((obj, index) => {
        const layerName = getObjectLayerName(obj, index);
        const isActive = canvas.getActiveObject() === obj;

        html += `<div class="layer-item ${isActive ? 'active' : ''}" data-index="${index}">`;
        html += '<div class="layer-info">';
        html += `<div class="layer-icon">${getObjectIcon(obj)}</div>`;
        html += `<div class="layer-name">${layerName}</div>`;
        html += '</div>';
        html += '<div class="layer-actions">';
        html += `<button class="layer-action" onclick="toggleLayerVisibility(${index})" title="Toggle Visibility">👁️</button>`;
        html += `<button class="layer-action" onclick="deleteLayer(${index})" title="Delete Layer">🗑️</button>`;
        html += '</div>';
        html += '</div>';
    });

    layersList.innerHTML = html;

    // Add click listeners for layer selection
    document.querySelectorAll('.layer-item').forEach(item => {
        item.addEventListener('click', function() {
            const index = parseInt(this.getAttribute('data-index'));
            const obj = canvas.getObjects()[index];
            canvas.setActiveObject(obj);
            canvas.renderAll();
            updateLayersList();
        });
    });
}

/**
 * Get object layer name
 */
function getObjectLayerName(obj, index) {
    if (obj.type === 'i-text' || obj.type === 'text') {
        return obj.text.length > 20 ? obj.text.substring(0, 20) + '...' : obj.text;
    } else if (obj.type === 'image') {
        return `Image ${index + 1}`;
    } else {
        return `${obj.type} ${index + 1}`;
    }
}

/**
 * Get object icon
 */
function getObjectIcon(obj) {
    switch (obj.type) {
        case 'i-text':
        case 'text':
            return '📝';
        case 'image':
            return '🖼️';
        case 'rect':
            return '⬜';
        case 'circle':
            return '⭕';
        default:
            return '📦';
    }
}

/**
 * Toggle layer visibility
 */
function toggleLayerVisibility(index) {
    const obj = canvas.getObjects()[index];
    obj.set('visible', !obj.visible);
    canvas.renderAll();
    updateLayersList();
}

/**
 * Delete layer
 */
function deleteLayer(index) {
    const obj = canvas.getObjects()[index];
    canvas.remove(obj);
    canvas.renderAll();
    updateLayersList();
}

/**
 * Open save modal
 */
function openSaveModal() {
    if (canvas.getObjects().length === 0) {
        showNotification('Please add some design elements before saving', 'warning');
        return;
    }

    document.getElementById('saveModal').classList.add('show');
}

/**
 * Open preview modal
 */
function openPreviewModal() {
    if (canvas.getObjects().length === 0) {
        showNotification('Please add some design elements before previewing', 'warning');
        return;
    }

    // Copy canvas to preview
    const canvasData = canvas.toJSON();
    previewCanvas.loadFromJSON(canvasData, function() {
        previewCanvas.renderAll();
    });

    // Update preview info
    const colorName = document.querySelector('.tshirt-color-option.active').getAttribute('data-name');
    document.getElementById('previewColor').textContent = colorName;
    document.getElementById('previewSizes').textContent = selectedSizes.join(', ');
    document.getElementById('previewDimensions').textContent = `${canvas.width}x${canvas.height}px`;

    document.getElementById('previewModal').classList.add('show');
}

/**
 * Save design as product
 */
function saveDesignAsProduct(e) {
    e.preventDefault();

    const formData = new FormData();
    formData.append('productName', document.getElementById('productName').value);
    formData.append('productDescription', document.getElementById('productDescription').value);
    formData.append('productPrice', document.getElementById('productPrice').value);
    formData.append('productTags', document.getElementById('productTags').value);
    formData.append('tshirtColor', currentTshirtColor);
    formData.append('selectedSizes', JSON.stringify(selectedSizes));
    formData.append('designData', JSON.stringify(canvas.toJSON()));

    // Generate design image
    const designImage = canvas.toDataURL('image/png');
    formData.append('designImage', designImage);

    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Saving...';
    submitBtn.disabled = true;

    // Send to server
    fetch('../php/save-design.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Design saved successfully!', 'success');
            document.getElementById('saveModal').classList.remove('show');

            // Reset form
            document.getElementById('saveDesignForm').reset();

            // Redirect to products page
            setTimeout(() => {
                window.location.href = 'products.php';
            }, 1500);
        } else {
            showNotification(data.message || 'Failed to save design', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while saving', 'error');
    })
    .finally(() => {
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    });
}
