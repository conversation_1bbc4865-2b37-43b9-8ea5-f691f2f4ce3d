/**
 * Authentication Pages Styles
 * Multi-Tenant Print-on-Demand Platform
 */

/* Auth page layout */
.auth-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.auth-container {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 40px;
    max-width: 1000px;
    width: 100%;
    align-items: start;
}

/* Auth card */
.auth-card {
    background: white;
    border-radius: 12px;
    padding: 40px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    width: 100%;
    max-width: 400px;
}

.auth-header {
    text-align: center;
    margin-bottom: 30px;
}

.auth-header h1 {
    color: #1F2937;
    font-size: 28px;
    font-weight: 700;
    margin: 0 0 8px 0;
}

.auth-header p {
    color: #6B7280;
    font-size: 16px;
    margin: 0;
}

/* Form styles */
.auth-form {
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 6px;
    font-size: 14px;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="password"] {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #E5E7EB;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.2s, box-shadow 0.2s;
    box-sizing: border-box;
}

.form-group input:focus {
    outline: none;
    border-color: #3B82F6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-help {
    display: block;
    font-size: 12px;
    color: #6B7280;
    margin-top: 4px;
}

/* Form row for checkbox and forgot password */
.form-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Checkbox styles */
.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: #374151;
    user-select: none;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #D1D5DB;
    border-radius: 4px;
    margin-right: 8px;
    position: relative;
    transition: all 0.2s;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background-color: #3B82F6;
    border-color: #3B82F6;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 5px;
    top: 2px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Links */
.forgot-link {
    color: #3B82F6;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
}

.forgot-link:hover {
    text-decoration: underline;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s;
    text-align: center;
}

.btn-primary {
    background-color: #3B82F6;
    color: white;
}

.btn-primary:hover {
    background-color: #2563EB;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.btn-full {
    width: 100%;
}

/* Alerts */
.alert {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 14px;
}

.alert ul {
    margin: 0;
    padding-left: 20px;
}

.alert-error {
    background-color: #FEF2F2;
    color: #B91C1C;
    border: 1px solid #FECACA;
}

.alert-success {
    background-color: #F0FDF4;
    color: #166534;
    border: 1px solid #BBF7D0;
}

/* Auth footer */
.auth-footer {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #E5E7EB;
}

.auth-footer p {
    margin: 8px 0;
    color: #6B7280;
    font-size: 14px;
}

.auth-footer a {
    color: #3B82F6;
    text-decoration: none;
    font-weight: 500;
}

.auth-footer a:hover {
    text-decoration: underline;
}

/* Side content */
.auth-demo,
.auth-benefits {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 30px;
    color: white;
    min-width: 280px;
}

.auth-demo h3,
.auth-benefits h3 {
    margin: 0 0 20px 0;
    font-size: 20px;
    font-weight: 600;
}

.demo-credentials {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 16px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.6;
}

.auth-benefits ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.auth-benefits li {
    padding: 8px 0;
    font-size: 14px;
    line-height: 1.5;
}

/* Responsive design */
@media (max-width: 768px) {
    .auth-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .auth-card {
        padding: 30px 20px;
    }
    
    .auth-demo,
    .auth-benefits {
        order: -1;
        padding: 20px;
        min-width: auto;
    }
    
    .form-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}

@media (max-width: 480px) {
    .auth-page {
        padding: 10px;
    }
    
    .auth-card {
        padding: 20px 15px;
    }
    
    .auth-header h1 {
        font-size: 24px;
    }
}
