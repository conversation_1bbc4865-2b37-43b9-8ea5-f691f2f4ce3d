<?php
/**
 * Setup Script for PrintShop Platform
 * Run this file once to initialize the database and create necessary directories
 */

require_once 'config/config.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup - PrintShop Platform</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .setup-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .btn {
            background-color: #3B82F6;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background-color: #2563EB;
        }
        h1 {
            color: #1F2937;
            text-align: center;
        }
        .step {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #3B82F6;
            background-color: #f8fafc;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <h1>PrintShop Platform Setup</h1>
        
        <?php
        $setupComplete = false;
        $errors = [];
        $warnings = [];
        $success = [];
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['run_setup'])) {
            
            // Step 1: Test database connection
            echo '<div class="step"><h3>Step 1: Testing Database Connection</h3>';
            if (testDatabaseConnection()) {
                echo '<div class="status success">✓ Database connection successful</div>';
                $success[] = 'Database connection established';
            } else {
                echo '<div class="status error">✗ Database connection failed</div>';
                $errors[] = 'Cannot connect to database. Please check your configuration in config/database.php';
            }
            echo '</div>';
            
            // Step 2: Create database tables
            if (empty($errors)) {
                echo '<div class="step"><h3>Step 2: Creating Database Tables</h3>';
                if (executeSQLFile('database/schema.sql')) {
                    echo '<div class="status success">✓ Database tables created successfully</div>';
                    $success[] = 'Database schema initialized';
                } else {
                    echo '<div class="status error">✗ Failed to create database tables</div>';
                    $errors[] = 'Could not execute database schema. Check file permissions and SQL syntax.';
                }
                echo '</div>';
            }
            
            // Step 3: Create upload directories
            echo '<div class="step"><h3>Step 3: Creating Upload Directories</h3>';
            try {
                createUploadDirectories();
                echo '<div class="status success">✓ Upload directories created</div>';
                $success[] = 'Upload directories created';
            } catch (Exception $e) {
                echo '<div class="status error">✗ Failed to create upload directories: ' . $e->getMessage() . '</div>';
                $errors[] = 'Could not create upload directories. Check file permissions.';
            }
            echo '</div>';
            
            // Step 4: Check file permissions
            echo '<div class="step"><h3>Step 4: Checking File Permissions</h3>';
            $uploadDirs = [UPLOAD_PATH, UPLOAD_DESIGNS_PATH, UPLOAD_MOCKUPS_PATH, UPLOAD_LOGOS_PATH, UPLOAD_AVATARS_PATH];
            $permissionIssues = false;
            
            foreach ($uploadDirs as $dir) {
                if (is_writable($dir)) {
                    echo '<div class="status success">✓ ' . basename($dir) . ' directory is writable</div>';
                } else {
                    echo '<div class="status warning">⚠ ' . basename($dir) . ' directory is not writable</div>';
                    $warnings[] = basename($dir) . ' directory needs write permissions';
                    $permissionIssues = true;
                }
            }
            
            if (!$permissionIssues) {
                $success[] = 'All directories have proper permissions';
            }
            echo '</div>';
            
            // Step 5: Verify admin user
            if (empty($errors)) {
                echo '<div class="step"><h3>Step 5: Verifying Admin User</h3>';
                try {
                    $db = getDB();
                    $stmt = $db->prepare("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
                    $stmt->execute();
                    $result = $stmt->fetch();
                    
                    if ($result['count'] > 0) {
                        echo '<div class="status success">✓ Admin user exists</div>';
                        $success[] = 'Admin user verified';
                    } else {
                        echo '<div class="status warning">⚠ No admin user found</div>';
                        $warnings[] = 'No admin user found in database';
                    }
                } catch (Exception $e) {
                    echo '<div class="status error">✗ Could not verify admin user: ' . $e->getMessage() . '</div>';
                    $errors[] = 'Could not verify admin user';
                }
                echo '</div>';
            }
            
            // Setup summary
            echo '<div class="step"><h3>Setup Summary</h3>';
            
            if (!empty($success)) {
                foreach ($success as $msg) {
                    echo '<div class="status success">✓ ' . $msg . '</div>';
                }
            }
            
            if (!empty($warnings)) {
                foreach ($warnings as $msg) {
                    echo '<div class="status warning">⚠ ' . $msg . '</div>';
                }
            }
            
            if (!empty($errors)) {
                foreach ($errors as $msg) {
                    echo '<div class="status error">✗ ' . $msg . '</div>';
                }
            } else {
                $setupComplete = true;
                echo '<div class="status success"><strong>✓ Setup completed successfully!</strong></div>';
                echo '<p><strong>Default Admin Credentials:</strong></p>';
                echo '<p>Email: <EMAIL><br>Password: admin123</p>';
                echo '<p><strong>Important:</strong> Please change the default admin password after logging in.</p>';
            }
            echo '</div>';
            
        } else {
            // Show setup form
            ?>
            <p>Welcome to the PrintShop Platform setup wizard. This will:</p>
            <ul>
                <li>Test your database connection</li>
                <li>Create necessary database tables</li>
                <li>Set up upload directories</li>
                <li>Verify file permissions</li>
                <li>Create the default admin user</li>
            </ul>
            
            <p><strong>Before proceeding, make sure:</strong></p>
            <ul>
                <li>Your web server (Apache/PHP) is running</li>
                <li>MySQL database is running</li>
                <li>Database credentials are correct in <code>config/database.php</code></li>
                <li>The web server has write permissions to the project directory</li>
            </ul>
            
            <form method="POST">
                <button type="submit" name="run_setup" class="btn">Run Setup</button>
            </form>
            <?php
        }
        
        if ($setupComplete) {
            echo '<div style="text-align: center; margin-top: 30px;">';
            echo '<a href="dashboard/login.php" class="btn">Go to Login Page</a> ';
            echo '<a href="admin/dashboard.php" class="btn">Go to Admin Panel</a>';
            echo '</div>';
        }
        ?>
    </div>
</body>
</html>
