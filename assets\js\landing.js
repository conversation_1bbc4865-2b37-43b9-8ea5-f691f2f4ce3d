/**
 * Landing Page JavaScript
 * Multi-Tenant Print-on-Demand Platform
 */

document.addEventListener('DOMContentLoaded', function() {
    initNavbarScroll();
    initSmoothScrolling();
    initMobileNavigation();
    initAnimationsOnScroll();
});

/**
 * Navbar scroll effect
 */
function initNavbarScroll() {
    const navbar = document.getElementById('navbar');
    
    if (!navbar) return;
    
    function handleScroll() {
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    }
    
    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Initial check
}

/**
 * Smooth scrolling for anchor links
 */
function initSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            
            // Skip if it's just "#"
            if (href === '#') return;
            
            const target = document.querySelector(href);
            
            if (target) {
                e.preventDefault();
                
                const navbarHeight = document.getElementById('navbar')?.offsetHeight || 0;
                const targetPosition = target.offsetTop - navbarHeight - 20;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
}

/**
 * Mobile navigation toggle
 */
function initMobileNavigation() {
    const navbarToggle = document.getElementById('navbarToggle');
    const navbarNav = document.querySelector('.navbar-nav');
    
    if (!navbarToggle || !navbarNav) return;
    
    navbarToggle.addEventListener('click', function() {
        navbarNav.classList.toggle('show');
        this.classList.toggle('active');
        
        // Toggle hamburger animation
        const spans = this.querySelectorAll('span');
        spans.forEach((span, index) => {
            if (this.classList.contains('active')) {
                if (index === 0) span.style.transform = 'rotate(45deg) translate(5px, 5px)';
                if (index === 1) span.style.opacity = '0';
                if (index === 2) span.style.transform = 'rotate(-45deg) translate(7px, -6px)';
            } else {
                span.style.transform = '';
                span.style.opacity = '';
            }
        });
    });
    
    // Close mobile nav when clicking on a link
    const navLinks = navbarNav.querySelectorAll('a');
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            navbarNav.classList.remove('show');
            navbarToggle.classList.remove('active');
            
            // Reset hamburger
            const spans = navbarToggle.querySelectorAll('span');
            spans.forEach(span => {
                span.style.transform = '';
                span.style.opacity = '';
            });
        });
    });
    
    // Close mobile nav when clicking outside
    document.addEventListener('click', function(e) {
        if (!navbarToggle.contains(e.target) && !navbarNav.contains(e.target)) {
            navbarNav.classList.remove('show');
            navbarToggle.classList.remove('active');
            
            // Reset hamburger
            const spans = navbarToggle.querySelectorAll('span');
            spans.forEach(span => {
                span.style.transform = '';
                span.style.opacity = '';
            });
        }
    });
    
    // Add mobile nav styles
    if (!document.getElementById('mobile-nav-styles')) {
        const style = document.createElement('style');
        style.id = 'mobile-nav-styles';
        style.textContent = `
            @media (max-width: 768px) {
                .navbar-nav {
                    position: absolute;
                    top: 100%;
                    left: 0;
                    right: 0;
                    background-color: var(--bg-primary);
                    border-top: 1px solid var(--border-primary);
                    box-shadow: var(--shadow-lg);
                    flex-direction: column;
                    padding: var(--space-4) 0;
                    transform: translateY(-100%);
                    opacity: 0;
                    visibility: hidden;
                    transition: all var(--transition-normal);
                }
                
                .navbar-nav.show {
                    transform: translateY(0);
                    opacity: 1;
                    visibility: visible;
                }
                
                .navbar-nav li {
                    margin: 0;
                }
                
                .navbar-nav a {
                    display: block;
                    padding: var(--space-3) var(--space-4);
                    border-bottom: 1px solid var(--border-primary);
                }
                
                .navbar-nav a:last-child {
                    border-bottom: none;
                }
                
                .navbar-toggle.active span:nth-child(1) {
                    transform: rotate(45deg) translate(5px, 5px);
                }
                
                .navbar-toggle.active span:nth-child(2) {
                    opacity: 0;
                }
                
                .navbar-toggle.active span:nth-child(3) {
                    transform: rotate(-45deg) translate(7px, -6px);
                }
            }
        `;
        document.head.appendChild(style);
    }
}

/**
 * Animations on scroll
 */
function initAnimationsOnScroll() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);
    
    // Observe elements that should animate
    const animateElements = document.querySelectorAll('.feature-card, .step-card, .hero-content');
    animateElements.forEach(el => {
        el.classList.add('animate-on-scroll');
        observer.observe(el);
    });
    
    // Add animation styles
    if (!document.getElementById('scroll-animation-styles')) {
        const style = document.createElement('style');
        style.id = 'scroll-animation-styles';
        style.textContent = `
            .animate-on-scroll {
                opacity: 0;
                transform: translateY(30px);
                transition: all 0.6s ease;
            }
            
            .animate-on-scroll.animate-in {
                opacity: 1;
                transform: translateY(0);
            }
            
            .feature-card.animate-on-scroll {
                transition-delay: 0.1s;
            }
            
            .feature-card:nth-child(2).animate-on-scroll {
                transition-delay: 0.2s;
            }
            
            .feature-card:nth-child(3).animate-on-scroll {
                transition-delay: 0.3s;
            }
            
            .step-card.animate-on-scroll {
                transition-delay: 0.1s;
            }
            
            .step-card:nth-child(2).animate-on-scroll {
                transition-delay: 0.2s;
            }
            
            .step-card:nth-child(3).animate-on-scroll {
                transition-delay: 0.3s;
            }
        `;
        document.head.appendChild(style);
    }
}

/**
 * Add floating animation to hero elements
 */
function initFloatingAnimation() {
    const style = document.createElement('style');
    style.textContent = `
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .hero-content {
            animation: float 6s ease-in-out infinite;
        }
        
        .feature-icon {
            animation: float 4s ease-in-out infinite;
        }
        
        .feature-card:nth-child(2) .feature-icon {
            animation-delay: 1s;
        }
        
        .feature-card:nth-child(3) .feature-icon {
            animation-delay: 2s;
        }
    `;
    document.head.appendChild(style);
}

// Initialize floating animation after a delay
setTimeout(initFloatingAnimation, 1000);

/**
 * Add parallax effect to hero section
 */
function initParallaxEffect() {
    const hero = document.querySelector('.hero');
    
    if (!hero) return;
    
    function handleParallax() {
        const scrolled = window.pageYOffset;
        const rate = scrolled * -0.5;
        
        hero.style.transform = `translateY(${rate}px)`;
    }
    
    // Only enable parallax on larger screens
    if (window.innerWidth > 768) {
        window.addEventListener('scroll', handleParallax);
    }
}

// Initialize parallax effect
initParallaxEffect();

/**
 * Add typing effect to hero title
 */
function initTypingEffect() {
    const heroTitle = document.querySelector('.hero h1');
    
    if (!heroTitle) return;
    
    const text = heroTitle.textContent;
    heroTitle.textContent = '';
    
    let i = 0;
    function typeWriter() {
        if (i < text.length) {
            heroTitle.textContent += text.charAt(i);
            i++;
            setTimeout(typeWriter, 100);
        }
    }
    
    // Start typing effect after a short delay
    setTimeout(typeWriter, 500);
}

// Uncomment to enable typing effect
// initTypingEffect();
