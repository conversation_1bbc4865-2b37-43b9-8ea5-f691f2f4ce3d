RewriteEngine On

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# Prevent access to sensitive files
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

<Files ~ "\.php$">
    Order allow,deny
    Allow from all
</Files>

# Prevent access to config files
<FilesMatch "\.(ini|log|conf)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache static files
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
</IfModule>

# URL Rewriting Rules

# Redirect root to public site
RewriteRule ^$ public-site/index.html [L]

# Store routing - /store/username
RewriteRule ^store/([a-zA-Z0-9_-]+)/?$ stores/index.php?store=$1 [L,QSA]
RewriteRule ^store/([a-zA-Z0-9_-]+)/product/([0-9]+)/?$ stores/product.php?store=$1&product=$2 [L,QSA]

# Admin routes
RewriteRule ^admin/?$ admin/dashboard.php [L]
RewriteRule ^admin/([a-zA-Z0-9_-]+)/?$ admin/$1.php [L,QSA]

# Dashboard routes
RewriteRule ^dashboard/?$ dashboard/index.php [L]
RewriteRule ^dashboard/([a-zA-Z0-9_-]+)/?$ dashboard/$1.php [L,QSA]

# API routes
RewriteRule ^api/([a-zA-Z0-9_-]+)/?$ php/api/$1.php [L,QSA]

# File upload size limits
php_value upload_max_filesize 5M
php_value post_max_size 5M
php_value max_execution_time 300
php_value max_input_time 300
