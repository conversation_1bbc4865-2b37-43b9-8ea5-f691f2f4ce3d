<?php
/**
 * User Profile Page
 * Multi-Tenant Print-on-Demand Platform
 */

require_once '../config/config.php';
require_once '../php/auth.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('login.php');
}

$user = getCurrentUser();
if (!$user) {
    redirect('login.php');
}

$errors = [];
$success = '';

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid security token. Please try again.';
    } else {
        $name = sanitizeInput($_POST['name'] ?? '');
        $email = sanitizeInput($_POST['email'] ?? '');
        $currentPassword = $_POST['current_password'] ?? '';
        $newPassword = $_POST['new_password'] ?? '';
        
        $result = Auth::updateProfile($user['id'], $name, $email, $currentPassword, $newPassword);
        
        if ($result['success']) {
            $success = $result['message'];
            // Refresh user data
            $user = getCurrentUser();
        } else {
            $errors = $result['errors'];
        }
    }
}

$pageTitle = 'Profile - ' . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/dashboard.css">
</head>
<body>
    <div class="dashboard-layout">
        <?php include '../includes/dashboard-sidebar.php'; ?>
        
        <main class="dashboard-main">
            <div class="dashboard-header">
                <h1>Profile Settings</h1>
                <p>Manage your account information and security settings</p>
            </div>
            
            <div class="dashboard-content">
                <div class="profile-container">
                    <div class="profile-card">
                        <div class="profile-header">
                            <div class="profile-avatar">
                                <img src="<?php echo $user['avatar_path'] ? UPLOAD_URL . '/avatars/' . $user['avatar_path'] : '../assets/images/default-avatar.png'; ?>" alt="Profile Picture">
                                <button class="avatar-upload-btn" type="button">
                                    <i class="icon-camera"></i>
                                </button>
                            </div>
                            <div class="profile-info">
                                <h2><?php echo htmlspecialchars($user['name']); ?></h2>
                                <p><?php echo htmlspecialchars($user['email']); ?></p>
                                <span class="profile-badge"><?php echo ucfirst($user['role']); ?></span>
                            </div>
                        </div>
                        
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-error">
                                <ul>
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <?php echo htmlspecialchars($success); ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" class="profile-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            
                            <div class="form-section">
                                <h3>Basic Information</h3>
                                
                                <div class="form-group">
                                    <label for="name">Full Name</label>
                                    <input 
                                        type="text" 
                                        id="name" 
                                        name="name" 
                                        value="<?php echo htmlspecialchars($user['name']); ?>" 
                                        required 
                                        minlength="2"
                                    >
                                </div>
                                
                                <div class="form-group">
                                    <label for="email">Email Address</label>
                                    <input 
                                        type="email" 
                                        id="email" 
                                        name="email" 
                                        value="<?php echo htmlspecialchars($user['email']); ?>" 
                                        required
                                    >
                                </div>
                            </div>
                            
                            <div class="form-section">
                                <h3>Change Password</h3>
                                <p class="form-help">Leave blank if you don't want to change your password</p>
                                
                                <div class="form-group">
                                    <label for="current_password">Current Password</label>
                                    <input 
                                        type="password" 
                                        id="current_password" 
                                        name="current_password" 
                                        autocomplete="current-password"
                                    >
                                </div>
                                
                                <div class="form-group">
                                    <label for="new_password">New Password</label>
                                    <input 
                                        type="password" 
                                        id="new_password" 
                                        name="new_password" 
                                        autocomplete="new-password"
                                        minlength="<?php echo PASSWORD_MIN_LENGTH; ?>"
                                    >
                                </div>
                            </div>
                            
                            <div class="form-actions">
                                <button type="submit" name="update_profile" class="btn btn-primary">
                                    Update Profile
                                </button>
                                <a href="index.php" class="btn btn-secondary">Cancel</a>
                            </div>
                        </form>
                    </div>
                    
                    <div class="profile-stats">
                        <h3>Account Statistics</h3>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-value">
                                    <?php
                                    try {
                                        $db = getDB();
                                        $stmt = $db->prepare("SELECT COUNT(*) as count FROM stores WHERE user_id = ?");
                                        $stmt->execute([$user['id']]);
                                        $result = $stmt->fetch();
                                        echo $result['count'];
                                    } catch (Exception $e) {
                                        echo '0';
                                    }
                                    ?>
                                </div>
                                <div class="stat-label">Stores</div>
                            </div>
                            
                            <div class="stat-item">
                                <div class="stat-value">
                                    <?php
                                    try {
                                        $stmt = $db->prepare("SELECT COUNT(*) as count FROM products p JOIN stores s ON p.store_id = s.id WHERE s.user_id = ?");
                                        $stmt->execute([$user['id']]);
                                        $result = $stmt->fetch();
                                        echo $result['count'];
                                    } catch (Exception $e) {
                                        echo '0';
                                    }
                                    ?>
                                </div>
                                <div class="stat-label">Products</div>
                            </div>
                            
                            <div class="stat-item">
                                <div class="stat-value">
                                    <?php
                                    try {
                                        $stmt = $db->prepare("SELECT COUNT(*) as count FROM orders o JOIN products p ON o.product_id = p.id JOIN stores s ON p.store_id = s.id WHERE s.user_id = ?");
                                        $stmt->execute([$user['id']]);
                                        $result = $stmt->fetch();
                                        echo $result['count'];
                                    } catch (Exception $e) {
                                        echo '0';
                                    }
                                    ?>
                                </div>
                                <div class="stat-label">Orders</div>
                            </div>
                            
                            <div class="stat-item">
                                <div class="stat-value"><?php echo formatDate($user['created_at'], 'M Y'); ?></div>
                                <div class="stat-label">Member Since</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/dashboard.js"></script>
</body>
</html>
