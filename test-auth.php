<?php
/**
 * Authentication Test Script
 * Use this to test registration and login functionality
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/config.php';
require_once 'php/auth.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #3B82F6;
            background-color: #f8fafc;
        }
        .success { color: #155724; background-color: #d4edda; border-color: #c3e6cb; }
        .error { color: #721c24; background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { color: #856404; background-color: #fff3cd; border-color: #ffeaa7; }
        .info { color: #0c5460; background-color: #d1ecf1; border-color: #bee5eb; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .btn {
            background-color: #3B82F6;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background-color: #2563EB; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Authentication Test</h1>
        
        <?php
        // Test 1: Check if tables exist
        echo '<div class="test-section">';
        echo '<h3>1. Database Tables Check</h3>';
        
        $requiredTables = ['users', 'stores'];
        $missingTables = [];
        
        foreach ($requiredTables as $table) {
            if (DatabaseUtils::tableExists($table)) {
                echo '<p class="success">✓ Table "' . $table . '" exists</p>';
            } else {
                echo '<p class="error">✗ Table "' . $table . '" missing</p>';
                $missingTables[] = $table;
            }
        }
        
        if (!empty($missingTables)) {
            echo '<p class="error">Missing tables: ' . implode(', ', $missingTables) . '</p>';
            echo '<p>Please run the setup first or create tables manually.</p>';
        }
        echo '</div>';
        
        // Test 2: Test Registration
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_register'])) {
            echo '<div class="test-section">';
            echo '<h3>2. Registration Test</h3>';
            
            $testName = $_POST['test_name'] ?? 'Test User';
            $testEmail = $_POST['test_email'] ?? '<EMAIL>';
            $testPassword = $_POST['test_password'] ?? 'testpass123';
            
            $result = Auth::register($testName, $testEmail, $testPassword, $testPassword);
            
            if ($result['success']) {
                echo '<p class="success">✓ Registration successful!</p>';
                echo '<p>User ID: ' . $result['user_id'] . '</p>';
                echo '<p>Message: ' . $result['message'] . '</p>';
            } else {
                echo '<p class="error">✗ Registration failed</p>';
                foreach ($result['errors'] as $error) {
                    echo '<p class="error">Error: ' . $error . '</p>';
                }
            }
            echo '</div>';
        }
        
        // Test 3: Test Login
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_login'])) {
            echo '<div class="test-section">';
            echo '<h3>3. Login Test</h3>';
            
            $testEmail = $_POST['login_email'] ?? '<EMAIL>';
            $testPassword = $_POST['login_password'] ?? 'testpass123';
            
            $result = Auth::login($testEmail, $testPassword);
            
            if ($result['success']) {
                echo '<p class="success">✓ Login successful!</p>';
                echo '<p>User: ' . $result['user']['name'] . ' (' . $result['user']['email'] . ')</p>';
                echo '<p>Role: ' . $result['user']['role'] . '</p>';
                echo '<p>Message: ' . $result['message'] . '</p>';
            } else {
                echo '<p class="error">✗ Login failed</p>';
                foreach ($result['errors'] as $error) {
                    echo '<p class="error">Error: ' . $error . '</p>';
                }
            }
            echo '</div>';
        }
        
        // Test 4: Check current session
        echo '<div class="test-section">';
        echo '<h3>4. Session Status</h3>';
        
        if (isLoggedIn()) {
            $user = getCurrentUser();
            echo '<p class="success">✓ User is logged in</p>';
            echo '<p>Name: ' . $user['name'] . '</p>';
            echo '<p>Email: ' . $user['email'] . '</p>';
            echo '<p>Role: ' . $user['role'] . '</p>';
        } else {
            echo '<p class="info">ℹ No user logged in</p>';
        }
        echo '</div>';
        ?>
        
        <!-- Registration Test Form -->
        <div class="test-section">
            <h3>Test Registration</h3>
            <form method="POST">
                <div class="form-group">
                    <label>Name:</label>
                    <input type="text" name="test_name" value="Test User <?php echo rand(1, 999); ?>" required>
                </div>
                <div class="form-group">
                    <label>Email:</label>
                    <input type="email" name="test_email" value="test<?php echo rand(1, 999); ?>@example.com" required>
                </div>
                <div class="form-group">
                    <label>Password:</label>
                    <input type="password" name="test_password" value="testpass123" required>
                </div>
                <button type="submit" name="test_register" class="btn">Test Registration</button>
            </form>
        </div>
        
        <!-- Login Test Form -->
        <div class="test-section">
            <h3>Test Login</h3>
            <form method="POST">
                <div class="form-group">
                    <label>Email:</label>
                    <input type="email" name="login_email" value="<EMAIL>" required>
                </div>
                <div class="form-group">
                    <label>Password:</label>
                    <input type="password" name="login_password" value="admin123" required>
                </div>
                <button type="submit" name="test_login" class="btn">Test Login</button>
            </form>
            <p><small><NAME_EMAIL> / admin123 (default admin) or any user you registered above</small></p>
        </div>
        
        <!-- Quick Actions -->
        <div class="test-section">
            <h3>Quick Actions</h3>
            <a href="setup.php" class="btn">Run Setup</a>
            <a href="database-test.php" class="btn">Database Test</a>
            <a href="dashboard/register.php" class="btn">Real Registration</a>
            <a href="dashboard/login.php" class="btn">Real Login</a>
            <?php if (isLoggedIn()): ?>
                <a href="php/logout.php" class="btn">Logout</a>
            <?php endif; ?>
        </div>
        
        <!-- Debug Info -->
        <div class="test-section">
            <h3>Debug Information</h3>
            <p><strong>Session ID:</strong> <?php echo session_id(); ?></p>
            <p><strong>Session Data:</strong></p>
            <pre><?php print_r($_SESSION); ?></pre>
            
            <p><strong>Database Connection:</strong></p>
            <?php
            try {
                $db = getDB();
                echo '<p class="success">✓ Database connected</p>';
                
                $stmt = $db->query("SELECT COUNT(*) as user_count FROM users");
                $result = $stmt->fetch();
                echo '<p>Total users in database: ' . $result['user_count'] . '</p>';
                
            } catch (Exception $e) {
                echo '<p class="error">✗ Database error: ' . $e->getMessage() . '</p>';
            }
            ?>
        </div>
    </div>
</body>
</html>
