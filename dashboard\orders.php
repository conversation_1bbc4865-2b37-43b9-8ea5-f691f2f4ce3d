<?php
/**
 * Orders Management Page
 * Multi-Tenant Print-on-Demand Platform
 */

require_once '../config/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('login.php');
}

$user = getCurrentUser();
if (!$user) {
    redirect('login.php');
}

// Get user's store and orders
try {
    $db = getDB();
    
    // Get store
    $stmt = $db->prepare("SELECT * FROM stores WHERE user_id = ? LIMIT 1");
    $stmt->execute([$user['id']]);
    $store = $stmt->fetch();
    
    // Get orders
    $orders = [];
    if ($store) {
        $stmt = $db->prepare("
            SELECT o.*, p.name as product_name, p.mockup_path
            FROM orders o
            JOIN products p ON o.product_id = p.id
            WHERE p.store_id = ?
            ORDER BY o.created_at DESC
        ");
        $stmt->execute([$store['id']]);
        $orders = $stmt->fetchAll();
    }
    
} catch (Exception $e) {
    error_log("Orders fetch error: " . $e->getMessage());
    $store = null;
    $orders = [];
}

$pageTitle = 'Orders - ' . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/dashboard.css">
    <link rel="stylesheet" href="../assets/css/orders.css">
</head>
<body>
    <div class="dashboard-layout">
        <?php include '../includes/dashboard-sidebar.php'; ?>
        
        <main class="dashboard-main">
            <div class="dashboard-header">
                <div>
                    <h1>Orders</h1>
                    <p>Manage your customer orders and track fulfillment</p>
                </div>
                <div class="header-actions">
                    <button class="btn btn-outline" onclick="exportOrders()">
                        <i class="icon-download"></i>
                        Export
                    </button>
                    <button class="btn btn-primary" onclick="refreshOrders()">
                        <i class="icon-refresh"></i>
                        Refresh
                    </button>
                </div>
            </div>
            
            <div class="dashboard-content">
                <?php if (empty($orders)): ?>
                    <div class="empty-state">
                        <div class="empty-state-icon">📦</div>
                        <h3>No Orders Yet</h3>
                        <p>When customers purchase your products, their orders will appear here.</p>
                        <div class="empty-actions">
                            <a href="products.php" class="btn btn-primary">View Products</a>
                            <a href="design-editor.php" class="btn btn-outline">Create Design</a>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Order Statistics -->
                    <div class="order-stats">
                        <?php
                        $totalOrders = count($orders);
                        $pendingOrders = array_filter($orders, fn($o) => $o['status'] === 'pending');
                        $completedOrders = array_filter($orders, fn($o) => $o['status'] === 'delivered');
                        $totalRevenue = array_sum(array_column($orders, 'total_amount'));
                        ?>
                        
                        <div class="stat-card">
                            <div class="stat-value"><?php echo $totalOrders; ?></div>
                            <div class="stat-label">Total Orders</div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-value"><?php echo count($pendingOrders); ?></div>
                            <div class="stat-label">Pending</div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-value"><?php echo count($completedOrders); ?></div>
                            <div class="stat-label">Completed</div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-value"><?php echo formatPrice($totalRevenue); ?></div>
                            <div class="stat-label">Total Revenue</div>
                        </div>
                    </div>
                    
                    <!-- Orders Table -->
                    <div class="orders-table-container">
                        <table class="orders-table">
                            <thead>
                                <tr>
                                    <th>Order</th>
                                    <th>Product</th>
                                    <th>Customer</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($orders as $order): ?>
                                    <tr class="order-row">
                                        <td>
                                            <div class="order-info">
                                                <strong>#<?php echo $order['order_number']; ?></strong>
                                                <small><?php echo $order['size']; ?> • <?php echo $order['color']; ?></small>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="product-info">
                                                <img src="<?php echo UPLOAD_URL . '/mockups/' . $order['mockup_path']; ?>" 
                                                     alt="<?php echo htmlspecialchars($order['product_name']); ?>"
                                                     class="product-thumbnail">
                                                <div>
                                                    <strong><?php echo htmlspecialchars($order['product_name']); ?></strong>
                                                    <small>Qty: <?php echo $order['quantity']; ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="customer-info">
                                                <strong><?php echo htmlspecialchars($order['customer_name']); ?></strong>
                                                <small><?php echo htmlspecialchars($order['customer_email']); ?></small>
                                            </div>
                                        </td>
                                        <td>
                                            <strong><?php echo formatPrice($order['total_amount']); ?></strong>
                                        </td>
                                        <td>
                                            <span class="status-badge status-<?php echo $order['status']; ?>">
                                                <?php echo ucfirst($order['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="date-info">
                                                <strong><?php echo formatDate($order['created_at'], 'M d, Y'); ?></strong>
                                                <small><?php echo formatDate($order['created_at'], 'g:i A'); ?></small>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="order-actions">
                                                <button class="action-btn" onclick="viewOrder(<?php echo $order['id']; ?>)" title="View Details">
                                                    👁️
                                                </button>
                                                <button class="action-btn" onclick="updateOrderStatus(<?php echo $order['id']; ?>)" title="Update Status">
                                                    ✏️
                                                </button>
                                                <button class="action-btn" onclick="printOrder(<?php echo $order['id']; ?>)" title="Print">
                                                    🖨️
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>
    
    <!-- Order Details Modal -->
    <div id="orderModal" class="modal">
        <div class="modal-backdrop">
            <div class="modal-content modal-lg">
                <div class="modal-header">
                    <h3 id="orderModalTitle">Order Details</h3>
                    <button class="modal-close" data-modal-close>&times;</button>
                </div>
                <div class="modal-body" id="orderModalBody">
                    <!-- Content will be loaded dynamically -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-modal-close>Close</button>
                    <button type="button" class="btn btn-primary" id="updateStatusBtn">Update Status</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/orders.js"></script>
</body>
</html>
