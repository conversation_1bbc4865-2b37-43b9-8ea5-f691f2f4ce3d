<?php
/**
 * Table Debug Script
 * Use this to see exactly what's happening with table creation
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/config.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Table Debug Tool</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #3B82F6;
            background-color: #f8fafc;
        }
        .success { color: #155724; background-color: #d4edda; border-color: #c3e6cb; }
        .error { color: #721c24; background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { color: #856404; background-color: #fff3cd; border-color: #ffeaa7; }
        .info { color: #0c5460; background-color: #d1ecf1; border-color: #bee5eb; }
        pre { background: #f4f4f4; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
        .btn {
            background-color: #3B82F6;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover { background-color: #2563EB; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Table Debug Tool</h1>
        
        <?php
        // Test 1: Current Database Info
        echo '<div class="test-section">';
        echo '<h3>1. Current Database Information</h3>';
        
        try {
            $db = getDB();
            
            // Get current database
            $stmt = $db->query("SELECT DATABASE() as current_db");
            $result = $stmt->fetch();
            echo '<p><strong>Current Database:</strong> ' . ($result['current_db'] ?? 'None') . '</p>';
            echo '<p><strong>Expected Database:</strong> ' . DB_NAME . '</p>';
            
            if ($result['current_db'] !== DB_NAME) {
                echo '<p class="warning">⚠ Database mismatch! Attempting to switch...</p>';
                $db->exec("USE `" . DB_NAME . "`");
                
                $stmt = $db->query("SELECT DATABASE() as current_db");
                $result = $stmt->fetch();
                echo '<p><strong>After switch:</strong> ' . ($result['current_db'] ?? 'None') . '</p>';
            }
            
        } catch (Exception $e) {
            echo '<p class="error">✗ Database error: ' . $e->getMessage() . '</p>';
        }
        echo '</div>';
        
        // Test 2: List all databases
        echo '<div class="test-section">';
        echo '<h3>2. Available Databases</h3>';
        
        try {
            $db = getDB();
            $stmt = $db->query("SHOW DATABASES");
            $databases = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            echo '<ul>';
            foreach ($databases as $dbName) {
                $highlight = ($dbName === DB_NAME) ? ' <strong>(TARGET)</strong>' : '';
                echo '<li>' . $dbName . $highlight . '</li>';
            }
            echo '</ul>';
            
        } catch (Exception $e) {
            echo '<p class="error">✗ Error listing databases: ' . $e->getMessage() . '</p>';
        }
        echo '</div>';
        
        // Test 3: List all tables in target database
        echo '<div class="test-section">';
        echo '<h3>3. Tables in Target Database (' . DB_NAME . ')</h3>';
        
        try {
            $db = getDB();
            $db->exec("USE `" . DB_NAME . "`");
            
            $stmt = $db->query("SHOW TABLES");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            if (empty($tables)) {
                echo '<p class="warning">⚠ No tables found in database "' . DB_NAME . '"</p>';
            } else {
                echo '<p class="success">✓ Found ' . count($tables) . ' tables:</p>';
                echo '<ul>';
                foreach ($tables as $table) {
                    echo '<li>' . $table . '</li>';
                }
                echo '</ul>';
            }
            
        } catch (Exception $e) {
            echo '<p class="error">✗ Error listing tables: ' . $e->getMessage() . '</p>';
        }
        echo '</div>';
        
        // Test 4: Check specific tables using different methods
        echo '<div class="test-section">';
        echo '<h3>4. Table Existence Check (Multiple Methods)</h3>';
        
        $requiredTables = ['users', 'stores', 'products', 'orders', 'user_sessions', 'settings'];
        
        try {
            $db = getDB();
            $db->exec("USE `" . DB_NAME . "`");
            
            echo '<table>';
            echo '<tr><th>Table</th><th>SHOW TABLES</th><th>INFORMATION_SCHEMA</th><th>DatabaseUtils</th></tr>';
            
            foreach ($requiredTables as $table) {
                echo '<tr>';
                echo '<td>' . $table . '</td>';
                
                // Method 1: SHOW TABLES
                try {
                    $stmt = $db->prepare("SHOW TABLES LIKE ?");
                    $stmt->execute([$table]);
                    $exists1 = $stmt->rowCount() > 0;
                    echo '<td class="' . ($exists1 ? 'success' : 'error') . '">' . ($exists1 ? '✓' : '✗') . '</td>';
                } catch (Exception $e) {
                    echo '<td class="error">Error</td>';
                }
                
                // Method 2: INFORMATION_SCHEMA
                try {
                    $stmt = $db->prepare("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = ? AND table_name = ?");
                    $stmt->execute([DB_NAME, $table]);
                    $result = $stmt->fetch();
                    $exists2 = $result['count'] > 0;
                    echo '<td class="' . ($exists2 ? 'success' : 'error') . '">' . ($exists2 ? '✓' : '✗') . '</td>';
                } catch (Exception $e) {
                    echo '<td class="error">Error</td>';
                }
                
                // Method 3: DatabaseUtils
                try {
                    $exists3 = DatabaseUtils::tableExists($table);
                    echo '<td class="' . ($exists3 ? 'success' : 'error') . '">' . ($exists3 ? '✓' : '✗') . '</td>';
                } catch (Exception $e) {
                    echo '<td class="error">Error</td>';
                }
                
                echo '</tr>';
            }
            echo '</table>';
            
        } catch (Exception $e) {
            echo '<p class="error">✗ Error checking tables: ' . $e->getMessage() . '</p>';
        }
        echo '</div>';
        
        // Test 5: Manual table creation test
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_single_table'])) {
            echo '<div class="test-section">';
            echo '<h3>5. Single Table Creation Test</h3>';
            
            try {
                $db = getDB();
                $db->exec("USE `" . DB_NAME . "`");
                
                // Try creating just the users table
                $usersSql = "CREATE TABLE IF NOT EXISTS users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    email VARCHAR(150) UNIQUE NOT NULL,
                    password VARCHAR(255) NOT NULL,
                    role ENUM('user', 'admin') DEFAULT 'user',
                    avatar_path VARCHAR(255) DEFAULT NULL,
                    email_verified TINYINT(1) DEFAULT 0,
                    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_email (email),
                    INDEX idx_role (role),
                    INDEX idx_status (status)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
                
                $db->exec($usersSql);
                echo '<p class="success">✓ Users table created successfully</p>';
                
                // Verify it exists
                $stmt = $db->prepare("SHOW TABLES LIKE 'users'");
                $stmt->execute();
                if ($stmt->rowCount() > 0) {
                    echo '<p class="success">✓ Users table verified</p>';
                    
                    // Show table structure
                    $stmt = $db->query("DESCRIBE users");
                    $columns = $stmt->fetchAll();
                    
                    echo '<p><strong>Table Structure:</strong></p>';
                    echo '<table>';
                    echo '<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>';
                    foreach ($columns as $column) {
                        echo '<tr>';
                        echo '<td>' . $column['Field'] . '</td>';
                        echo '<td>' . $column['Type'] . '</td>';
                        echo '<td>' . $column['Null'] . '</td>';
                        echo '<td>' . $column['Key'] . '</td>';
                        echo '<td>' . $column['Default'] . '</td>';
                        echo '</tr>';
                    }
                    echo '</table>';
                } else {
                    echo '<p class="error">✗ Users table not found after creation</p>';
                }
                
            } catch (Exception $e) {
                echo '<p class="error">✗ Table creation failed: ' . $e->getMessage() . '</p>';
            }
            echo '</div>';
        }
        
        // Test 6: Show current database content
        echo '<div class="test-section">';
        echo '<h3>6. Database Content Summary</h3>';
        
        try {
            $db = getDB();
            $db->exec("USE `" . DB_NAME . "`");
            
            $stmt = $db->query("
                SELECT 
                    table_name,
                    table_rows,
                    data_length,
                    index_length,
                    create_time
                FROM information_schema.tables 
                WHERE table_schema = '" . DB_NAME . "'
                ORDER BY table_name
            ");
            $tableInfo = $stmt->fetchAll();
            
            if (empty($tableInfo)) {
                echo '<p class="warning">⚠ No tables found in database</p>';
            } else {
                echo '<table>';
                echo '<tr><th>Table</th><th>Rows</th><th>Data Size</th><th>Created</th></tr>';
                foreach ($tableInfo as $info) {
                    echo '<tr>';
                    echo '<td>' . $info['table_name'] . '</td>';
                    echo '<td>' . $info['table_rows'] . '</td>';
                    echo '<td>' . number_format($info['data_length']) . ' bytes</td>';
                    echo '<td>' . $info['create_time'] . '</td>';
                    echo '</tr>';
                }
                echo '</table>';
            }
            
        } catch (Exception $e) {
            echo '<p class="error">✗ Error getting database info: ' . $e->getMessage() . '</p>';
        }
        echo '</div>';
        ?>
        
        <!-- Actions -->
        <div class="test-section">
            <h3>Actions</h3>
            <form method="POST" style="display: inline;">
                <button type="submit" name="create_single_table" class="btn">Test Single Table Creation</button>
            </form>
            <a href="setup.php" class="btn">Run Setup</a>
            <a href="database-test.php" class="btn">Database Test</a>
            <a href="test-auth.php" class="btn">Auth Test</a>
        </div>
        
        <!-- SQL Commands -->
        <div class="test-section">
            <h3>Manual SQL Commands</h3>
            <p>If tables are missing, you can run these commands manually in phpMyAdmin:</p>
            <pre>USE `<?php echo DB_NAME; ?>`;

CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(150) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('user', 'admin') DEFAULT 'user',
    avatar_path VARCHAR(255) DEFAULT NULL,
    email_verified TINYINT(1) DEFAULT 0,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS stores (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    store_name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    logo_path VARCHAR(255) DEFAULT NULL,
    banner_path VARCHAR(255) DEFAULT NULL,
    theme_color VARCHAR(7) DEFAULT '#3B82F6',
    accent_color VARCHAR(7) DEFAULT '#10B981',
    custom_css TEXT,
    is_active TINYINT(1) DEFAULT 1,
    total_products INT DEFAULT 0,
    total_orders INT DEFAULT 0,
    total_revenue DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_slug (slug),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;</pre>
        </div>
    </div>
</body>
</html>
