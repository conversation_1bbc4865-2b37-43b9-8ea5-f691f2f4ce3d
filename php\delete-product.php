<?php
/**
 * Delete Product
 * Multi-Tenant Print-on-Demand Platform
 */

require_once '../config/config.php';

// Set JSON response header
header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'User not authenticated']);
    exit;
}

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

try {
    $user = getCurrentUser();
    if (!$user) {
        echo json_encode(['success' => false, 'message' => 'User not found']);
        exit;
    }
    
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['product_id']) || empty($input['product_id'])) {
        echo json_encode(['success' => false, 'message' => 'Product ID is required']);
        exit;
    }
    
    $productId = intval($input['product_id']);
    
    $db = getDB();
    
    // Get product with store verification (ensure user owns the product)
    $stmt = $db->prepare("
        SELECT p.*, s.user_id 
        FROM products p 
        JOIN stores s ON p.store_id = s.id 
        WHERE p.id = ? AND s.user_id = ?
    ");
    $stmt->execute([$productId, $user['id']]);
    $product = $stmt->fetch();
    
    if (!$product) {
        echo json_encode(['success' => false, 'message' => 'Product not found or access denied']);
        exit;
    }
    
    // Delete associated files
    $designPath = UPLOAD_DESIGNS_PATH . '/' . $product['design_path'];
    $mockupPath = UPLOAD_MOCKUPS_PATH . '/' . $product['mockup_path'];
    
    if (file_exists($designPath)) {
        unlink($designPath);
    }
    
    if (file_exists($mockupPath)) {
        unlink($mockupPath);
    }
    
    // Delete product from database
    $stmt = $db->prepare("DELETE FROM products WHERE id = ?");
    $result = $stmt->execute([$productId]);
    
    if ($result) {
        // Update store product count
        $stmt = $db->prepare("UPDATE stores SET total_products = total_products - 1 WHERE id = ?");
        $stmt->execute([$product['store_id']]);
        
        echo json_encode([
            'success' => true,
            'message' => 'Product deleted successfully'
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to delete product']);
    }
    
} catch (Exception $e) {
    error_log("Delete product error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while deleting product']);
}
?>
