/**
 * Store Frontend Styles
 * Multi-Tenant Print-on-Demand Platform
 */

/* Store Page Layout */
.store-page {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Store Header */
.store-header {
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-primary);
    padding: var(--space-6) 0;
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    backdrop-filter: blur(10px);
}

.store-brand {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    margin-bottom: var(--space-4);
}

.store-logo {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: var(--radius-xl);
    border: 2px solid var(--border-primary);
}

.store-info {
    flex: 1;
}

.store-name {
    margin: 0 0 var(--space-2) 0;
    color: var(--text-primary);
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
}

.store-tagline {
    margin: 0;
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
    line-height: var(--line-height-relaxed);
}

/* Store Navigation */
.store-nav {
    display: flex;
    gap: var(--space-6);
    border-top: 1px solid var(--border-primary);
    padding-top: var(--space-4);
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
}

.nav-link:hover {
    color: var(--color-primary);
    background-color: var(--primary-50);
}

.nav-link.active {
    color: var(--color-primary);
    background-color: var(--primary-100);
    font-weight: var(--font-weight-semibold);
}

/* Store Main */
.store-main {
    flex: 1;
    padding: var(--space-8) 0;
    background-color: var(--bg-secondary);
}

/* Empty Store */
.empty-store {
    text-align: center;
    padding: var(--space-20) var(--space-8);
    background-color: var(--bg-primary);
    border-radius: var(--radius-2xl);
    border: 2px dashed var(--border-secondary);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: var(--space-6);
}

.empty-store h2 {
    margin: 0 0 var(--space-4) 0;
    color: var(--text-primary);
    font-size: var(--font-size-3xl);
}

.empty-store p {
    margin: 0;
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
    max-width: 500px;
    margin: 0 auto;
}

/* Products Header */
.products-header {
    text-align: center;
    margin-bottom: var(--space-12);
}

.products-header h2 {
    margin: 0 0 var(--space-4) 0;
    color: var(--text-primary);
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
}

.products-header p {
    margin: 0;
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
}

/* Products Grid */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--space-6);
}

.product-card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    overflow: hidden;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.product-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--color-primary);
}

.product-link {
    display: block;
    text-decoration: none;
    color: inherit;
}

.product-image {
    position: relative;
    aspect-ratio: 1;
    overflow: hidden;
    background-color: var(--bg-secondary);
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.view-product {
    background-color: white;
    color: var(--text-primary);
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-lg);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-sm);
}

.product-details {
    padding: var(--space-6);
}

.product-name {
    margin: 0 0 var(--space-2) 0;
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
}

.product-price {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-primary);
    margin-bottom: var(--space-3);
}

.product-colors {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.color-dot {
    width: 20px;
    height: 20px;
    border-radius: var(--radius-full);
    border: 2px solid var(--border-primary);
    cursor: pointer;
    transition: transform var(--transition-fast);
}

.color-dot:hover {
    transform: scale(1.2);
}

.more-colors {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    font-weight: var(--font-weight-medium);
}

/* Product Detail Page */
.product-detail {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-12);
    background-color: var(--bg-primary);
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--space-12);
}

.product-gallery {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.main-image {
    aspect-ratio: 1;
    border-radius: var(--radius-xl);
    overflow: hidden;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
}

.main-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.color-options {
    margin-top: var(--space-4);
}

.color-options h4 {
    margin: 0 0 var(--space-3) 0;
    color: var(--text-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
}

.color-swatches {
    display: flex;
    gap: var(--space-3);
}

.color-swatch {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    border: 3px solid transparent;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.color-swatch:hover {
    transform: scale(1.1);
    border-color: var(--color-primary);
}

.color-swatch.active {
    border-color: var(--color-primary);
    transform: scale(1.1);
}

/* Product Info */
.breadcrumb {
    margin-bottom: var(--space-6);
}

.breadcrumb a {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    transition: color var(--transition-fast);
}

.breadcrumb a:hover {
    color: var(--color-primary);
}

.product-title {
    margin: 0 0 var(--space-4) 0;
    color: var(--text-primary);
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
}

.product-description {
    margin-bottom: var(--space-6);
    padding-bottom: var(--space-6);
    border-bottom: 1px solid var(--border-primary);
}

.product-description p {
    color: var(--text-secondary);
    line-height: var(--line-height-relaxed);
}

/* Order Form */
.order-form {
    background-color: var(--bg-secondary);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    margin-bottom: var(--space-6);
}

.order-form .form-group {
    margin-bottom: var(--space-4);
}

.order-form label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-2);
}

.total-price {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-primary);
    text-align: center;
    margin: var(--space-6) 0;
    padding: var(--space-4);
    background-color: var(--primary-50);
    border-radius: var(--radius-lg);
}

/* Product Stats */
.product-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: var(--space-6);
    padding: var(--space-4);
    background-color: var(--bg-secondary);
    border-radius: var(--radius-lg);
}

.stat {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-primary);
    margin-bottom: var(--space-1);
}

.stat-label {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Product Share */
.product-share {
    border-top: 1px solid var(--border-primary);
    padding-top: var(--space-6);
}

.product-share h4 {
    margin: 0 0 var(--space-4) 0;
    color: var(--text-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
}

.share-buttons {
    display: flex;
    gap: var(--space-3);
    flex-wrap: wrap;
}

.share-btn {
    padding: var(--space-2) var(--space-4);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-fast);
    cursor: pointer;
    background-color: var(--bg-primary);
    color: var(--text-secondary);
}

.share-btn:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    transform: translateY(-1px);
}

/* Related Products */
.related-products {
    margin-top: var(--space-12);
}

.related-products h2 {
    margin: 0 0 var(--space-8) 0;
    color: var(--text-primary);
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    text-align: center;
}

/* Store Footer */
.store-footer {
    background-color: var(--bg-primary);
    border-top: 1px solid var(--border-primary);
    padding: var(--space-12) 0 var(--space-6) 0;
    margin-top: var(--space-12);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-8);
    margin-bottom: var(--space-8);
}

.footer-content h3 {
    margin: 0 0 var(--space-4) 0;
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
}

.footer-content p {
    color: var(--text-secondary);
    line-height: var(--line-height-relaxed);
}

.contact-info p {
    margin-bottom: var(--space-2);
}

.contact-info a {
    color: var(--color-primary);
    text-decoration: none;
}

.contact-info a:hover {
    text-decoration: underline;
}

.social-links {
    display: flex;
    gap: var(--space-3);
    margin-top: var(--space-4);
}

.social-link {
    padding: var(--space-2) var(--space-4);
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    text-decoration: none;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-fast);
}

.social-link:hover {
    background-color: var(--color-primary);
    color: white;
    transform: translateY(-1px);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-4);
}

.footer-bottom {
    text-align: center;
    padding-top: var(--space-6);
    border-top: 1px solid var(--border-primary);
    color: var(--text-tertiary);
    font-size: var(--font-size-sm);
}

.footer-bottom p {
    margin: var(--space-2) 0;
}

.footer-bottom a {
    color: var(--color-primary);
    text-decoration: none;
}

.footer-bottom a:hover {
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .product-detail {
        grid-template-columns: 1fr;
        gap: var(--space-8);
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .store-header {
        padding: var(--space-4) 0;
    }
    
    .store-brand {
        flex-direction: column;
        text-align: center;
        margin-bottom: var(--space-6);
    }
    
    .store-name {
        font-size: var(--font-size-2xl);
    }
    
    .store-nav {
        justify-content: center;
        flex-wrap: wrap;
        gap: var(--space-3);
    }
    
    .store-main {
        padding: var(--space-6) 0;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: var(--space-4);
    }
    
    .product-detail {
        padding: var(--space-6);
        gap: var(--space-6);
    }
    
    .product-title {
        font-size: var(--font-size-2xl);
    }
    
    .share-buttons {
        justify-content: center;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--space-6);
        text-align: center;
    }
    
    .social-links {
        justify-content: center;
    }
    
    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--space-3);
    }
}

@media (max-width: 480px) {
    .store-logo {
        width: 60px;
        height: 60px;
    }
    
    .store-name {
        font-size: var(--font-size-xl);
    }
    
    .store-tagline {
        font-size: var(--font-size-base);
    }
    
    .products-grid {
        grid-template-columns: 1fr;
    }
    
    .product-detail {
        padding: var(--space-4);
    }
    
    .product-stats {
        flex-direction: column;
        gap: var(--space-3);
    }
    
    .share-buttons {
        flex-direction: column;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}
