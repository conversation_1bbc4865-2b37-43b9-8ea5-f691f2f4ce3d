/**
 * Dashboard JavaScript
 * Multi-Tenant Print-on-Demand Platform
 */

document.addEventListener('DOMContentLoaded', function() {
    initDashboard();
});

/**
 * Initialize dashboard functionality
 */
function initDashboard() {
    initStatsAnimation();
    initQuickActions();
    initRealtimeUpdates();
    console.log('Dashboard initialized');
}

/**
 * Animate statistics cards on load
 */
function initStatsAnimation() {
    const statCards = document.querySelectorAll('.dashboard-card');
    
    statCards.forEach((card, index) => {
        // Add animation delay based on index
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('animate-slide-up');
        
        // Animate the number counting
        const valueElement = card.querySelector('.dashboard-card-value');
        if (valueElement) {
            animateNumber(valueElement);
        }
    });
}

/**
 * Animate number counting effect
 */
function animateNumber(element) {
    const finalValue = element.textContent;
    const numericValue = parseFloat(finalValue.replace(/[^0-9.-]+/g, ''));
    
    if (isNaN(numericValue)) return;
    
    const duration = 1000; // 1 second
    const steps = 30;
    const stepValue = numericValue / steps;
    const stepDuration = duration / steps;
    
    let currentValue = 0;
    let currentStep = 0;
    
    const timer = setInterval(() => {
        currentStep++;
        currentValue += stepValue;
        
        if (currentStep >= steps) {
            currentValue = numericValue;
            clearInterval(timer);
        }
        
        // Format the number based on original format
        let displayValue;
        if (finalValue.includes('₹')) {
            displayValue = '₹' + Math.floor(currentValue).toLocaleString();
        } else if (finalValue.includes(',')) {
            displayValue = Math.floor(currentValue).toLocaleString();
        } else {
            displayValue = Math.floor(currentValue).toString();
        }
        
        element.textContent = displayValue;
    }, stepDuration);
}

/**
 * Initialize quick actions
 */
function initQuickActions() {
    const actionCards = document.querySelectorAll('.action-card');
    
    actionCards.forEach(card => {
        card.addEventListener('click', function(e) {
            // Add click animation
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });
}

/**
 * Initialize real-time updates (placeholder for future implementation)
 */
function initRealtimeUpdates() {
    // This would connect to a WebSocket or use polling to update stats in real-time
    // For now, we'll just set up the structure
    
    // Example: Update stats every 5 minutes
    setInterval(() => {
        updateDashboardStats();
    }, 5 * 60 * 1000);
}

/**
 * Update dashboard statistics
 */
function updateDashboardStats() {
    fetch('../php/get-dashboard-stats.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStatCard('total-products', data.stats.total_products);
                updateStatCard('total-orders', data.stats.total_orders);
                updateStatCard('total-revenue', data.stats.total_revenue);
                updateStatCard('total-views', data.stats.total_views);
            }
        })
        .catch(error => {
            console.error('Error updating stats:', error);
        });
}

/**
 * Update individual stat card
 */
function updateStatCard(cardId, newValue) {
    const card = document.getElementById(cardId);
    if (!card) return;
    
    const valueElement = card.querySelector('.dashboard-card-value');
    if (!valueElement) return;
    
    // Add update animation
    valueElement.style.opacity = '0.5';
    
    setTimeout(() => {
        if (cardId === 'total-revenue') {
            valueElement.textContent = '₹' + parseFloat(newValue).toLocaleString();
        } else {
            valueElement.textContent = parseInt(newValue).toLocaleString();
        }
        
        valueElement.style.opacity = '1';
        
        // Add pulse effect to indicate update
        valueElement.style.animation = 'pulse 0.5s ease';
        setTimeout(() => {
            valueElement.style.animation = '';
        }, 500);
    }, 200);
}

/**
 * Copy store URL to clipboard
 */
function copyStoreUrl() {
    const storeUrlElement = document.querySelector('.store-url a');
    if (!storeUrlElement) return;
    
    const url = storeUrlElement.href;
    
    if (navigator.clipboard) {
        navigator.clipboard.writeText(url).then(() => {
            showNotification('Store URL copied to clipboard!', 'success');
        }).catch(err => {
            console.error('Failed to copy: ', err);
            fallbackCopyTextToClipboard(url);
        });
    } else {
        fallbackCopyTextToClipboard(url);
    }
}

/**
 * Fallback copy function for older browsers
 */
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    
    // Avoid scrolling to bottom
    textArea.style.top = '0';
    textArea.style.left = '0';
    textArea.style.position = 'fixed';
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showNotification('Store URL copied to clipboard!', 'success');
        } else {
            showNotification('Failed to copy URL', 'error');
        }
    } catch (err) {
        console.error('Fallback: Oops, unable to copy', err);
        showNotification('Failed to copy URL', 'error');
    }
    
    document.body.removeChild(textArea);
}

/**
 * Refresh dashboard data
 */
function refreshDashboard() {
    // Show loading state
    const refreshBtn = document.getElementById('refreshBtn');
    if (refreshBtn) {
        refreshBtn.disabled = true;
        refreshBtn.innerHTML = '<i class="icon-loading"></i> Refreshing...';
    }
    
    // Update stats
    updateDashboardStats();
    
    // Reload recent products and orders
    setTimeout(() => {
        window.location.reload();
    }, 1000);
}

/**
 * Quick share store
 */
function quickShareStore() {
    const storeUrlElement = document.querySelector('.store-url a');
    if (!storeUrlElement) return;
    
    const url = storeUrlElement.href;
    const text = 'Check out my custom t-shirt store!';
    
    if (navigator.share) {
        navigator.share({
            title: 'My T-Shirt Store',
            text: text,
            url: url
        }).then(() => {
            console.log('Store shared successfully');
        }).catch(err => {
            console.log('Error sharing:', err);
            copyStoreUrl(); // Fallback to copy
        });
    } else {
        copyStoreUrl(); // Fallback to copy
    }
}

/**
 * Show welcome tour for new users
 */
function showWelcomeTour() {
    // This would show a guided tour for new users
    // Implementation would depend on a tour library like Intro.js or Shepherd.js
    console.log('Welcome tour would start here');
}

/**
 * Export dashboard data
 */
function exportDashboardData() {
    const data = {
        exported_at: new Date().toISOString(),
        stats: {
            total_products: document.querySelector('.dashboard-card:nth-child(1) .dashboard-card-value')?.textContent || '0',
            total_orders: document.querySelector('.dashboard-card:nth-child(2) .dashboard-card-value')?.textContent || '0',
            total_revenue: document.querySelector('.dashboard-card:nth-child(3) .dashboard-card-value')?.textContent || '₹0',
            total_views: document.querySelector('.dashboard-card:nth-child(4) .dashboard-card-value')?.textContent || '0'
        }
    };
    
    const dataStr = JSON.stringify(data, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `dashboard-data-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    
    showNotification('Dashboard data exported successfully', 'success');
}

/**
 * Handle keyboard shortcuts
 */
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + D: Go to design editor
    if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
        e.preventDefault();
        window.location.href = 'design-editor.php';
    }
    
    // Ctrl/Cmd + P: Go to products
    if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
        e.preventDefault();
        window.location.href = 'products.php';
    }
    
    // Ctrl/Cmd + O: Go to orders
    if ((e.ctrlKey || e.metaKey) && e.key === 'o') {
        e.preventDefault();
        window.location.href = 'orders.php';
    }
    
    // Ctrl/Cmd + R: Refresh dashboard
    if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
        e.preventDefault();
        refreshDashboard();
    }
});

// Add keyboard shortcuts info to help
function showKeyboardShortcuts() {
    const shortcuts = [
        'Ctrl/Cmd + D: Create New Design',
        'Ctrl/Cmd + P: View Products',
        'Ctrl/Cmd + O: View Orders',
        'Ctrl/Cmd + R: Refresh Dashboard'
    ];
    
    showNotification('Keyboard Shortcuts:\n' + shortcuts.join('\n'), 'info', 5000);
}

// Expose functions globally for use in HTML
window.copyStoreUrl = copyStoreUrl;
window.refreshDashboard = refreshDashboard;
window.quickShareStore = quickShareStore;
window.showWelcomeTour = showWelcomeTour;
window.exportDashboardData = exportDashboardData;
window.showKeyboardShortcuts = showKeyboardShortcuts;
