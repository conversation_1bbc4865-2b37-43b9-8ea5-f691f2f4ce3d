/**
 * Design Editor Styles
 * Multi-Tenant Print-on-Demand Platform
 */

/* Design Editor Container */
.design-editor-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: var(--bg-secondary);
}

/* Editor Header */
.editor-header {
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-primary);
    padding: var(--space-4) var(--space-6);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
}

.editor-title h1 {
    margin: 0 0 var(--space-1) 0;
    font-size: var(--font-size-xl);
    color: var(--text-primary);
}

.editor-title p {
    margin: 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.editor-actions {
    display: flex;
    gap: var(--space-3);
}

/* Editor Layout */
.editor-layout {
    flex: 1;
    display: grid;
    grid-template-columns: 280px 1fr 300px;
    gap: 0;
    overflow: hidden;
}

/* Tools Panel */
.tools-panel {
    background-color: var(--bg-primary);
    border-right: 1px solid var(--border-primary);
    padding: var(--space-4);
    overflow-y: auto;
}

.tool-section {
    margin-bottom: var(--space-6);
    padding-bottom: var(--space-4);
    border-bottom: 1px solid var(--border-primary);
}

.tool-section:last-child {
    border-bottom: none;
}

.tool-section h3 {
    margin: 0 0 var(--space-3) 0;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Upload Area */
.upload-area {
    border: 2px dashed var(--border-secondary);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    background-color: var(--bg-secondary);
}

.upload-area:hover {
    border-color: var(--color-primary);
    background-color: var(--primary-50);
}

.upload-area.dragover {
    border-color: var(--color-primary);
    background-color: var(--primary-50);
    transform: scale(1.02);
}

.upload-content i {
    font-size: 2rem;
    color: var(--text-tertiary);
    margin-bottom: var(--space-2);
}

.upload-content p {
    margin: 0 0 var(--space-1) 0;
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
}

.upload-content small {
    color: var(--text-tertiary);
    font-size: var(--font-size-xs);
}

/* Tool Buttons */
.tool-btn {
    width: 100%;
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3);
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    margin-bottom: var(--space-2);
}

.tool-btn:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-secondary);
}

.tool-btn.active {
    background-color: var(--primary-50);
    color: var(--color-primary);
    border-color: var(--color-primary);
}

/* Text Controls */
.text-controls {
    margin-top: var(--space-3);
}

.text-controls .form-control {
    margin-bottom: var(--space-3);
}

.range-slider {
    width: 100%;
    margin: var(--space-2) 0;
}

/* Color Picker */
.color-picker-container {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

#colorPicker {
    width: 100%;
    height: 40px;
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
}

.color-presets {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--space-2);
}

.color-preset {
    width: 30px;
    height: 30px;
    border-radius: var(--radius-md);
    cursor: pointer;
    border: 2px solid var(--border-primary);
    transition: all var(--transition-fast);
}

.color-preset:hover {
    transform: scale(1.1);
    border-color: var(--border-secondary);
}

.color-preset.active {
    border-color: var(--color-primary);
    transform: scale(1.1);
}

/* Canvas Area */
.canvas-area {
    background-color: var(--bg-tertiary);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--space-6);
    position: relative;
}

.canvas-container {
    background-color: var(--bg-primary);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: var(--shadow-lg);
    position: relative;
}

.tshirt-mockup {
    position: relative;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 500"><path d="M50 100 L50 50 Q50 30 70 30 L130 30 Q140 20 160 20 L240 20 Q260 20 270 30 L330 30 Q350 30 350 50 L350 100 L380 120 L380 180 L350 160 L350 450 Q350 470 330 470 L70 470 Q50 470 50 450 L50 160 L20 180 L20 120 Z" fill="%23ffffff" stroke="%23e5e7eb" stroke-width="2"/></svg>') center/contain no-repeat;
    width: 400px;
    height: 500px;
}

#designCanvas {
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    background-color: transparent;
}

/* Canvas Controls */
.canvas-controls {
    position: absolute;
    bottom: var(--space-4);
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    gap: var(--space-4);
    background-color: var(--bg-primary);
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-full);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-primary);
}

.zoom-controls {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.control-btn {
    width: 32px;
    height: 32px;
    border: 1px solid var(--border-primary);
    background-color: var(--bg-secondary);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    font-weight: var(--font-weight-bold);
}

.control-btn:hover {
    background-color: var(--bg-tertiary);
    border-color: var(--border-secondary);
}

#zoomLevel {
    min-width: 50px;
    text-align: center;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
}

/* Properties Panel */
.properties-panel {
    background-color: var(--bg-primary);
    border-left: 1px solid var(--border-primary);
    padding: var(--space-4);
    overflow-y: auto;
}

.panel-section {
    margin-bottom: var(--space-6);
    padding-bottom: var(--space-4);
    border-bottom: 1px solid var(--border-primary);
}

.panel-section:last-child {
    border-bottom: none;
}

.panel-section h3 {
    margin: 0 0 var(--space-3) 0;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* T-shirt Color Options */
.color-options {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: var(--space-2);
    margin-top: var(--space-2);
}

.tshirt-color-option {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    cursor: pointer;
    border: 3px solid transparent;
    transition: all var(--transition-fast);
    position: relative;
}

.tshirt-color-option:hover {
    transform: scale(1.1);
}

.tshirt-color-option.active {
    border-color: var(--color-primary);
    transform: scale(1.1);
}

.tshirt-color-option[style*="FFFFFF"] {
    border: 3px solid var(--border-primary);
}

.tshirt-color-option[style*="FFFFFF"].active {
    border-color: var(--color-primary);
}

/* Size Options */
.size-checkboxes {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-2);
    margin-top: var(--space-2);
}

.size-checkboxes .checkbox-label {
    font-size: var(--font-size-sm);
    padding: var(--space-2);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    text-align: center;
    transition: all var(--transition-fast);
}

.size-checkboxes .checkbox-label:hover {
    background-color: var(--bg-secondary);
}

.size-checkboxes input[type="checkbox"]:checked + .checkmark {
    background-color: var(--color-primary);
}

/* Object Properties */
.object-properties {
    min-height: 100px;
}

.no-selection {
    color: var(--text-tertiary);
    font-style: italic;
    text-align: center;
    padding: var(--space-4);
}

.property-group {
    margin-bottom: var(--space-4);
}

.property-group label {
    display: block;
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    margin-bottom: var(--space-1);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.property-group input,
.property-group select {
    width: 100%;
    padding: var(--space-2);
    font-size: var(--font-size-sm);
}

/* Layers List */
.layers-list {
    min-height: 100px;
}

.layer-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-2) var(--space-3);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    margin-bottom: var(--space-2);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.layer-item:hover {
    background-color: var(--bg-secondary);
}

.layer-item.active {
    background-color: var(--primary-50);
    border-color: var(--color-primary);
}

.layer-info {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.layer-icon {
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.layer-name {
    font-size: var(--font-size-sm);
    color: var(--text-primary);
}

.layer-actions {
    display: flex;
    gap: var(--space-1);
}

.layer-action {
    width: 20px;
    height: 20px;
    border: none;
    background: none;
    cursor: pointer;
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: var(--text-tertiary);
    transition: all var(--transition-fast);
}

.layer-action:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-secondary);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: var(--z-modal);
}

.modal.show {
    display: flex;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--bg-overlay);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-4);
}

.modal-content {
    background-color: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-lg {
    max-width: 800px;
}

.modal-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--text-tertiary);
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-secondary);
}

.modal-body {
    padding: var(--space-6);
}

.modal-footer {
    padding: var(--space-6);
    border-top: 1px solid var(--border-primary);
    display: flex;
    gap: var(--space-3);
    justify-content: flex-end;
}

/* Preview Styles */
.preview-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-6);
}

.preview-mockup {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
}

.preview-info h4 {
    margin: 0 0 var(--space-4) 0;
    color: var(--text-primary);
}

.preview-details p {
    margin: 0 0 var(--space-2) 0;
    font-size: var(--font-size-sm);
}

/* Icon placeholders */
.icon-save::before { content: '💾'; }
.icon-eye::before { content: '👁️'; }
.icon-upload::before { content: '📤'; }
.icon-text::before { content: '📝'; }
.icon-delete::before { content: '🗑️'; }
.icon-clear::before { content: '🧹'; }
.icon-undo::before { content: '↶'; }
.icon-redo::before { content: '↷'; }

/* Responsive Design */
@media (max-width: 1200px) {
    .editor-layout {
        grid-template-columns: 250px 1fr 250px;
    }
}

@media (max-width: 768px) {
    .editor-layout {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr auto;
    }
    
    .tools-panel,
    .properties-panel {
        max-height: 200px;
        overflow-y: auto;
    }
    
    .canvas-area {
        padding: var(--space-4);
    }
    
    .tshirt-mockup {
        width: 300px;
        height: 375px;
    }
    
    #designCanvas {
        width: 300px;
        height: 375px;
    }
    
    .preview-container {
        grid-template-columns: 1fr;
    }
}
