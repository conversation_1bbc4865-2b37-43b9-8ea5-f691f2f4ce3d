/**
 * Orders Page JavaScript
 * Multi-Tenant Print-on-Demand Platform
 */

document.addEventListener('DOMContentLoaded', function() {
    initOrdersPage();
});

/**
 * Initialize orders page
 */
function initOrdersPage() {
    console.log('Orders page initialized');
}

/**
 * View order details
 */
function viewOrder(orderId) {
    fetch(`../php/get-order.php?id=${orderId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showOrderModal(data.order);
            } else {
                showNotification(data.message || 'Failed to load order', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('An error occurred while loading order', 'error');
        });
}

/**
 * Show order details modal
 */
function showOrderModal(order) {
    const modal = document.getElementById('orderModal');
    const modalTitle = document.getElementById('orderModalTitle');
    const modalBody = document.getElementById('orderModalBody');
    
    modalTitle.textContent = `Order #${order.order_number}`;
    
    modalBody.innerHTML = `
        <div class="order-detail-grid">
            <div class="order-detail-section">
                <h4>Product Details</h4>
                <div class="order-product-preview">
                    <img src="../uploads/mockups/${order.mockup_path}" alt="${order.product_name}">
                </div>
                <div class="detail-row">
                    <span class="detail-label">Product:</span>
                    <span class="detail-value">${order.product_name}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Size:</span>
                    <span class="detail-value">${order.size}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Color:</span>
                    <span class="detail-value">${order.color}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Quantity:</span>
                    <span class="detail-value">${order.quantity}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Unit Price:</span>
                    <span class="detail-value">₹${parseFloat(order.unit_price).toFixed(2)}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Total Amount:</span>
                    <span class="detail-value"><strong>₹${parseFloat(order.total_amount).toFixed(2)}</strong></span>
                </div>
            </div>
            
            <div class="order-detail-section">
                <h4>Customer Information</h4>
                <div class="detail-row">
                    <span class="detail-label">Name:</span>
                    <span class="detail-value">${order.customer_name}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Email:</span>
                    <span class="detail-value">${order.customer_email}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Phone:</span>
                    <span class="detail-value">${order.customer_phone || 'Not provided'}</span>
                </div>
                
                <h4 style="margin-top: var(--space-4);">Shipping Address</h4>
                <div class="detail-row">
                    <span class="detail-label">Address:</span>
                    <span class="detail-value">${order.shipping_address}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">City:</span>
                    <span class="detail-value">${order.shipping_city}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">State:</span>
                    <span class="detail-value">${order.shipping_state}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">PIN Code:</span>
                    <span class="detail-value">${order.shipping_pincode}</span>
                </div>
            </div>
        </div>
        
        <div class="order-detail-section" style="margin-top: var(--space-6);">
            <h4>Order Status & Timeline</h4>
            <div class="detail-row">
                <span class="detail-label">Current Status:</span>
                <span class="detail-value">
                    <span class="status-badge status-${order.status}">
                        ${order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                    </span>
                </span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Order Date:</span>
                <span class="detail-value">${formatDateTime(order.created_at)}</span>
            </div>
            ${order.updated_at ? `
            <div class="detail-row">
                <span class="detail-label">Last Updated:</span>
                <span class="detail-value">${formatDateTime(order.updated_at)}</span>
            </div>
            ` : ''}
            
            <div class="status-update-form">
                <label for="orderStatus">Update Status:</label>
                <select id="orderStatus" class="status-select">
                    <option value="pending" ${order.status === 'pending' ? 'selected' : ''}>Pending</option>
                    <option value="confirmed" ${order.status === 'confirmed' ? 'selected' : ''}>Confirmed</option>
                    <option value="processing" ${order.status === 'processing' ? 'selected' : ''}>Processing</option>
                    <option value="shipped" ${order.status === 'shipped' ? 'selected' : ''}>Shipped</option>
                    <option value="delivered" ${order.status === 'delivered' ? 'selected' : ''}>Delivered</option>
                    <option value="cancelled" ${order.status === 'cancelled' ? 'selected' : ''}>Cancelled</option>
                </select>
                
                <textarea id="statusNote" class="status-note" placeholder="Add a note about this status update (optional)"></textarea>
            </div>
        </div>
    `;
    
    // Update the update button to handle this specific order
    const updateBtn = document.getElementById('updateStatusBtn');
    updateBtn.onclick = () => updateOrderStatusFromModal(order.id);
    
    modal.classList.add('show');
}

/**
 * Update order status from modal
 */
function updateOrderStatusFromModal(orderId) {
    const newStatus = document.getElementById('orderStatus').value;
    const note = document.getElementById('statusNote').value;
    
    updateOrderStatusAPI(orderId, newStatus, note);
}

/**
 * Update order status
 */
function updateOrderStatus(orderId) {
    // For quick status updates, show a simple prompt
    const newStatus = prompt('Enter new status (pending, confirmed, processing, shipped, delivered, cancelled):');
    
    if (newStatus && ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'].includes(newStatus.toLowerCase())) {
        updateOrderStatusAPI(orderId, newStatus.toLowerCase());
    } else if (newStatus) {
        showNotification('Invalid status. Please use: pending, confirmed, processing, shipped, delivered, or cancelled', 'error');
    }
}

/**
 * Update order status via API
 */
function updateOrderStatusAPI(orderId, newStatus, note = '') {
    fetch('../php/update-order-status.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            order_id: orderId,
            status: newStatus,
            note: note
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Order status updated successfully', 'success');
            
            // Close modal if open
            const modal = document.getElementById('orderModal');
            if (modal.classList.contains('show')) {
                modal.classList.remove('show');
            }
            
            // Refresh the page to show updated status
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showNotification(data.message || 'Failed to update order status', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while updating order status', 'error');
    });
}

/**
 * Print order
 */
function printOrder(orderId) {
    // Open order in new window for printing
    const printWindow = window.open(`../php/print-order.php?id=${orderId}`, '_blank');
    
    if (printWindow) {
        printWindow.onload = function() {
            printWindow.print();
        };
    } else {
        showNotification('Please allow popups to print orders', 'warning');
    }
}

/**
 * Export orders
 */
function exportOrders() {
    const format = prompt('Export format (csv/json):', 'csv');
    
    if (format && ['csv', 'json'].includes(format.toLowerCase())) {
        window.location.href = `../php/export-orders.php?format=${format.toLowerCase()}`;
    } else if (format) {
        showNotification('Invalid format. Please use csv or json', 'error');
    }
}

/**
 * Refresh orders
 */
function refreshOrders() {
    showNotification('Refreshing orders...', 'info');
    setTimeout(() => {
        window.location.reload();
    }, 1000);
}

/**
 * Format date and time
 */
function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

/**
 * Filter orders by status
 */
function filterOrdersByStatus(status) {
    const rows = document.querySelectorAll('.order-row');
    
    rows.forEach(row => {
        const statusBadge = row.querySelector('.status-badge');
        const orderStatus = statusBadge.className.split('status-')[1];
        
        if (status === 'all' || orderStatus === status) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

/**
 * Search orders
 */
function searchOrders(query) {
    const rows = document.querySelectorAll('.order-row');
    const searchTerm = query.toLowerCase();
    
    rows.forEach(row => {
        const orderNumber = row.querySelector('.order-info strong').textContent.toLowerCase();
        const productName = row.querySelector('.product-info strong').textContent.toLowerCase();
        const customerName = row.querySelector('.customer-info strong').textContent.toLowerCase();
        
        if (orderNumber.includes(searchTerm) || 
            productName.includes(searchTerm) || 
            customerName.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// Add search functionality if search input exists
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('orderSearch');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            searchOrders(this.value);
        });
    }
    
    const statusFilter = document.getElementById('statusFilter');
    if (statusFilter) {
        statusFilter.addEventListener('change', function() {
            filterOrdersByStatus(this.value);
        });
    }
});
