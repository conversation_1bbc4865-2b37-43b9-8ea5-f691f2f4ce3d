<?php
/**
 * Get Product Details
 * Multi-Tenant Print-on-Demand Platform
 */

require_once '../config/config.php';

// Set JSON response header
header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'User not authenticated']);
    exit;
}

// Check if product ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    echo json_encode(['success' => false, 'message' => 'Product ID is required']);
    exit;
}

try {
    $user = getCurrentUser();
    if (!$user) {
        echo json_encode(['success' => false, 'message' => 'User not found']);
        exit;
    }
    
    $productId = intval($_GET['id']);
    
    $db = getDB();
    
    // Get product with store verification (ensure user owns the product)
    $stmt = $db->prepare("
        SELECT p.*, s.user_id, s.slug as store_slug 
        FROM products p 
        JOIN stores s ON p.store_id = s.id 
        WHERE p.id = ? AND s.user_id = ?
    ");
    $stmt->execute([$productId, $user['id']]);
    $product = $stmt->fetch();
    
    if (!$product) {
        echo json_encode(['success' => false, 'message' => 'Product not found or access denied']);
        exit;
    }
    
    echo json_encode([
        'success' => true,
        'product' => $product
    ]);
    
} catch (Exception $e) {
    error_log("Get product error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while fetching product']);
}
?>
