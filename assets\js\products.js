/**
 * Products Page JavaScript
 * Multi-Tenant Print-on-Demand Platform
 */

document.addEventListener('DOMContentLoaded', function() {
    initProductActions();
});

/**
 * Initialize product action handlers
 */
function initProductActions() {
    // Add event listeners for product actions
    console.log('Products page initialized');
}

/**
 * Edit product
 */
function editProduct(productId) {
    // For now, redirect to design editor with product data
    // In a full implementation, you would load the product data into the editor
    window.location.href = `design-editor.php?edit=${productId}`;
}

/**
 * View product details
 */
function viewProduct(productId) {
    fetch(`../php/get-product.php?id=${productId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showProductModal(data.product);
            } else {
                showNotification(data.message || 'Failed to load product', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('An error occurred while loading product', 'error');
        });
}

/**
 * Show product details modal
 */
function showProductModal(product) {
    const modal = document.getElementById('productModal');
    const modalTitle = document.getElementById('modalTitle');
    const modalBody = document.getElementById('modalBody');
    
    modalTitle.textContent = product.name;
    
    const colors = JSON.parse(product.colors || '[]');
    const sizes = JSON.parse(product.sizes || '[]');
    
    modalBody.innerHTML = `
        <div class="product-detail-view">
            <div class="product-detail-image">
                <img src="../uploads/mockups/${product.mockup_path}" alt="${product.name}">
            </div>
            <div class="product-detail-info">
                <h4>Product Details</h4>
                
                <div class="detail-item">
                    <span class="detail-label">Description:</span>
                    <div class="detail-value">${product.description || 'No description'}</div>
                </div>
                
                <div class="detail-item">
                    <span class="detail-label">Price:</span>
                    <div class="detail-value">₹${parseFloat(product.base_price).toFixed(2)}</div>
                </div>
                
                <div class="detail-item">
                    <span class="detail-label">Available Sizes:</span>
                    <div class="detail-value">${sizes.join(', ')}</div>
                </div>
                
                <div class="detail-item">
                    <span class="detail-label">Colors:</span>
                    <div class="detail-value">
                        <div class="color-swatches">
                            ${colors.map(color => `
                                <div class="color-swatch" 
                                     style="background-color: ${color.hex};" 
                                     title="${color.name}">
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
                
                <div class="detail-item">
                    <span class="detail-label">Status:</span>
                    <div class="detail-value">
                        <span class="status-badge ${product.is_active ? 'active' : 'inactive'}">
                            ${product.is_active ? 'Active' : 'Inactive'}
                        </span>
                    </div>
                </div>
                
                <div class="detail-item">
                    <span class="detail-label">Views:</span>
                    <div class="detail-value">${product.views_count}</div>
                </div>
                
                <div class="detail-item">
                    <span class="detail-label">Orders:</span>
                    <div class="detail-value">${product.orders_count}</div>
                </div>
                
                <div class="detail-item">
                    <span class="detail-label">Created:</span>
                    <div class="detail-value">${formatDate(product.created_at)}</div>
                </div>
                
                ${product.tags ? `
                <div class="detail-item">
                    <span class="detail-label">Tags:</span>
                    <div class="detail-value">${product.tags}</div>
                </div>
                ` : ''}
            </div>
        </div>
    `;
    
    modal.classList.add('show');
}

/**
 * Duplicate product
 */
function duplicateProduct(productId) {
    if (!confirm('Are you sure you want to duplicate this product?')) {
        return;
    }
    
    fetch('../php/duplicate-product.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ product_id: productId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Product duplicated successfully', 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showNotification(data.message || 'Failed to duplicate product', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while duplicating product', 'error');
    });
}

/**
 * Delete product
 */
function deleteProduct(productId) {
    if (!confirm('Are you sure you want to delete this product? This action cannot be undone.')) {
        return;
    }
    
    fetch('../php/delete-product.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ product_id: productId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Product deleted successfully', 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showNotification(data.message || 'Failed to delete product', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while deleting product', 'error');
    });
}

/**
 * Share product
 */
function shareProduct(productId) {
    fetch(`../php/get-product.php?id=${productId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showShareModal(data.product);
            } else {
                showNotification(data.message || 'Failed to load product', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('An error occurred while loading product', 'error');
        });
}

/**
 * Show share modal
 */
function showShareModal(product) {
    const modal = document.getElementById('productModal');
    const modalTitle = document.getElementById('modalTitle');
    const modalBody = document.getElementById('modalBody');
    const modalFooter = document.getElementById('modalFooter');
    
    modalTitle.textContent = 'Share Product';
    
    const productUrl = `${window.location.origin}/stores/index.php?product=${product.id}`;
    const shareText = `Check out this awesome t-shirt design: ${product.name}`;
    
    modalBody.innerHTML = `
        <div class="share-container">
            <p>Share this product with your customers:</p>
            
            <div class="share-link">
                <input type="text" value="${productUrl}" readonly class="form-control" id="shareUrl">
                <button class="copy-btn" onclick="copyShareUrl()">📋</button>
            </div>
            
            <div class="share-options">
                <a href="https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(productUrl)}" 
                   target="_blank" class="share-option">
                    <div class="share-option-icon">📘</div>
                    <div class="share-option-label">Facebook</div>
                </a>
                
                <a href="https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(productUrl)}" 
                   target="_blank" class="share-option">
                    <div class="share-option-icon">🐦</div>
                    <div class="share-option-label">Twitter</div>
                </a>
                
                <a href="https://wa.me/?text=${encodeURIComponent(shareText + ' ' + productUrl)}" 
                   target="_blank" class="share-option">
                    <div class="share-option-icon">💬</div>
                    <div class="share-option-label">WhatsApp</div>
                </a>
                
                <a href="mailto:?subject=${encodeURIComponent(product.name)}&body=${encodeURIComponent(shareText + ' ' + productUrl)}" 
                   class="share-option">
                    <div class="share-option-icon">📧</div>
                    <div class="share-option-label">Email</div>
                </a>
            </div>
        </div>
    `;
    
    modalFooter.innerHTML = `
        <button type="button" class="btn btn-secondary" data-modal-close>Close</button>
        <button type="button" class="btn btn-primary" onclick="copyShareUrl()">Copy Link</button>
    `;
    
    modal.classList.add('show');
}

/**
 * Copy share URL to clipboard
 */
function copyShareUrl() {
    const shareUrlInput = document.getElementById('shareUrl');
    shareUrlInput.select();
    shareUrlInput.setSelectionRange(0, 99999); // For mobile devices
    
    try {
        document.execCommand('copy');
        showNotification('Link copied to clipboard!', 'success');
    } catch (err) {
        console.error('Failed to copy: ', err);
        showNotification('Failed to copy link', 'error');
    }
}

/**
 * Format date for display
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

/**
 * Toggle product status
 */
function toggleProductStatus(productId, currentStatus) {
    const newStatus = currentStatus ? 0 : 1;
    const action = newStatus ? 'activate' : 'deactivate';
    
    if (!confirm(`Are you sure you want to ${action} this product?`)) {
        return;
    }
    
    fetch('../php/toggle-product-status.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
            product_id: productId, 
            status: newStatus 
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`Product ${action}d successfully`, 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showNotification(data.message || `Failed to ${action} product`, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification(`An error occurred while ${action}ing product`, 'error');
    });
}
