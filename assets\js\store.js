/**
 * Store Frontend JavaScript
 * Multi-Tenant Print-on-Demand Platform
 */

document.addEventListener('DOMContentLoaded', function() {
    initStore();
});

/**
 * Initialize store functionality
 */
function initStore() {
    initProductForm();
    initColorSwatches();
    initSmoothScrolling();
    console.log('Store initialized');
}

/**
 * Initialize product order form
 */
function initProductForm() {
    const quantityInput = document.getElementById('quantity');
    const sizeSelect = document.getElementById('size');
    const colorSelect = document.getElementById('color');
    
    if (quantityInput) {
        quantityInput.addEventListener('input', updateTotalPrice);
    }
    
    if (sizeSelect) {
        sizeSelect.addEventListener('change', updateTotalPrice);
    }
    
    if (colorSelect) {
        colorSelect.addEventListener('change', updateProductColor);
    }
    
    // Initialize total price
    updateTotalPrice();
}

/**
 * Update total price based on quantity
 */
function updateTotalPrice() {
    const quantityInput = document.getElementById('quantity');
    const totalPriceElement = document.getElementById('totalPrice');
    
    if (!quantityInput || !totalPriceElement) return;
    
    const quantity = parseInt(quantityInput.value) || 1;
    const basePrice = parseFloat(totalPriceElement.dataset.basePrice || totalPriceElement.textContent.replace(/[^0-9.-]+/g, ''));
    
    if (!isNaN(basePrice)) {
        const total = basePrice * quantity;
        totalPriceElement.textContent = '₹' + total.toFixed(2);
    }
}

/**
 * Initialize color swatches
 */
function initColorSwatches() {
    const colorSwatches = document.querySelectorAll('.color-swatch');
    const colorSelect = document.getElementById('color');
    
    colorSwatches.forEach(swatch => {
        swatch.addEventListener('click', function() {
            // Update active state
            colorSwatches.forEach(s => s.classList.remove('active'));
            this.classList.add('active');
            
            // Update select dropdown if exists
            if (colorSelect) {
                const colorName = this.getAttribute('title');
                if (colorName) {
                    colorSelect.value = colorName;
                    updateProductColor();
                }
            }
        });
    });
}

/**
 * Update product color (could be used to change main image)
 */
function updateProductColor() {
    const colorSelect = document.getElementById('color');
    const mainImage = document.getElementById('mainProductImage');
    
    if (!colorSelect || !mainImage) return;
    
    const selectedColor = colorSelect.value;
    console.log('Color changed to:', selectedColor);
    
    // In a real implementation, you would change the main image
    // based on the selected color variant
}

/**
 * Proceed to checkout
 */
function proceedToCheckout() {
    const form = document.getElementById('orderForm');
    if (!form) return;
    
    const formData = new FormData(form);
    const orderData = {
        product_id: formData.get('product_id'),
        size: formData.get('size'),
        color: formData.get('color'),
        quantity: formData.get('quantity')
    };
    
    // Validate form
    if (!orderData.size || !orderData.color || !orderData.quantity) {
        showNotification('Please fill in all required fields', 'error');
        return;
    }
    
    if (parseInt(orderData.quantity) < 1) {
        showNotification('Quantity must be at least 1', 'error');
        return;
    }
    
    // Show checkout modal
    showCheckoutModal(orderData);
}

/**
 * Show checkout modal
 */
function showCheckoutModal(orderData) {
    const modal = document.getElementById('checkoutModal');
    const modalBody = document.getElementById('checkoutModalBody');
    
    if (!modal || !modalBody) return;
    
    // Calculate total
    const quantity = parseInt(orderData.quantity);
    const basePrice = parseFloat(document.getElementById('totalPrice').textContent.replace(/[^0-9.-]+/g, ''));
    const total = basePrice;
    
    modalBody.innerHTML = `
        <form id="checkoutForm" class="checkout-form">
            <div class="order-summary">
                <h4>Order Summary</h4>
                <div class="summary-item">
                    <span>Size:</span>
                    <span>${orderData.size}</span>
                </div>
                <div class="summary-item">
                    <span>Color:</span>
                    <span>${orderData.color}</span>
                </div>
                <div class="summary-item">
                    <span>Quantity:</span>
                    <span>${orderData.quantity}</span>
                </div>
                <div class="summary-total">
                    <span>Total:</span>
                    <span>₹${total.toFixed(2)}</span>
                </div>
            </div>
            
            <div class="customer-details">
                <h4>Customer Details</h4>
                
                <div class="form-group">
                    <label for="customerName">Full Name *</label>
                    <input type="text" id="customerName" name="customer_name" required class="form-control">
                </div>
                
                <div class="form-group">
                    <label for="customerEmail">Email *</label>
                    <input type="email" id="customerEmail" name="customer_email" required class="form-control">
                </div>
                
                <div class="form-group">
                    <label for="customerPhone">Phone Number</label>
                    <input type="tel" id="customerPhone" name="customer_phone" class="form-control">
                </div>
            </div>
            
            <div class="shipping-details">
                <h4>Shipping Address</h4>
                
                <div class="form-group">
                    <label for="shippingAddress">Address *</label>
                    <textarea id="shippingAddress" name="shipping_address" required class="form-control" rows="3"></textarea>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="shippingCity">City *</label>
                        <input type="text" id="shippingCity" name="shipping_city" required class="form-control">
                    </div>
                    
                    <div class="form-group">
                        <label for="shippingState">State *</label>
                        <input type="text" id="shippingState" name="shipping_state" required class="form-control">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="shippingPincode">PIN Code *</label>
                    <input type="text" id="shippingPincode" name="shipping_pincode" required class="form-control" pattern="[0-9]{6}">
                </div>
            </div>
            
            <input type="hidden" name="product_id" value="${orderData.product_id}">
            <input type="hidden" name="size" value="${orderData.size}">
            <input type="hidden" name="color" value="${orderData.color}">
            <input type="hidden" name="quantity" value="${orderData.quantity}">
            <input type="hidden" name="total_amount" value="${total}">
            
            <div class="checkout-actions">
                <button type="button" class="btn btn-secondary" data-modal-close>Cancel</button>
                <button type="submit" class="btn btn-primary">Place Order</button>
            </div>
        </form>
    `;
    
    // Add form submit handler
    const checkoutForm = document.getElementById('checkoutForm');
    checkoutForm.addEventListener('submit', handleCheckoutSubmit);
    
    modal.classList.add('show');
}

/**
 * Handle checkout form submission
 */
function handleCheckoutSubmit(e) {
    e.preventDefault();
    
    const form = e.target;
    const formData = new FormData(form);
    
    // Show loading state
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Processing...';
    submitBtn.disabled = true;
    
    // Submit order
    fetch('../php/place-order.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Order placed successfully! You will receive a confirmation email shortly.', 'success');
            
            // Close modal
            document.getElementById('checkoutModal').classList.remove('show');
            
            // Reset form
            document.getElementById('orderForm').reset();
            updateTotalPrice();
            
            // Redirect to thank you page or show order details
            if (data.order_id) {
                setTimeout(() => {
                    window.location.href = `?store=${getStoreSlug()}&order=${data.order_id}`;
                }, 2000);
            }
        } else {
            showNotification(data.message || 'Failed to place order', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while placing the order', 'error');
    })
    .finally(() => {
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    });
}

/**
 * Share product on social media
 */
function shareProduct(platform) {
    const url = window.location.href;
    const title = document.querySelector('.product-title')?.textContent || 'Check out this awesome t-shirt!';
    const text = `${title} - Custom designed t-shirt`;
    
    let shareUrl;
    
    switch (platform) {
        case 'facebook':
            shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
            break;
        case 'twitter':
            shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`;
            break;
        case 'whatsapp':
            shareUrl = `https://wa.me/?text=${encodeURIComponent(text + ' ' + url)}`;
            break;
        default:
            return;
    }
    
    window.open(shareUrl, '_blank', 'width=600,height=400');
}

/**
 * Copy product URL to clipboard
 */
function copyProductUrl() {
    const url = window.location.href;
    
    if (navigator.clipboard) {
        navigator.clipboard.writeText(url).then(() => {
            showNotification('Product link copied to clipboard!', 'success');
        }).catch(err => {
            console.error('Failed to copy: ', err);
            fallbackCopyTextToClipboard(url);
        });
    } else {
        fallbackCopyTextToClipboard(url);
    }
}

/**
 * Fallback copy function for older browsers
 */
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    
    textArea.style.top = '0';
    textArea.style.left = '0';
    textArea.style.position = 'fixed';
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showNotification('Product link copied to clipboard!', 'success');
        } else {
            showNotification('Failed to copy link', 'error');
        }
    } catch (err) {
        console.error('Fallback: Oops, unable to copy', err);
        showNotification('Failed to copy link', 'error');
    }
    
    document.body.removeChild(textArea);
}

/**
 * Initialize smooth scrolling for anchor links
 */
function initSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            
            if (href === '#') return;
            
            const target = document.querySelector(href);
            
            if (target) {
                e.preventDefault();
                
                const headerHeight = document.querySelector('.store-header')?.offsetHeight || 0;
                const targetPosition = target.offsetTop - headerHeight - 20;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
}

/**
 * Get store slug from URL
 */
function getStoreSlug() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('store');
}

/**
 * Add CSS for checkout form
 */
function addCheckoutStyles() {
    if (document.getElementById('checkout-styles')) return;
    
    const style = document.createElement('style');
    style.id = 'checkout-styles';
    style.textContent = `
        .checkout-form {
            max-width: 600px;
            margin: 0 auto;
        }
        
        .order-summary {
            background-color: var(--bg-secondary);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            margin-bottom: var(--space-6);
        }
        
        .order-summary h4 {
            margin: 0 0 var(--space-4) 0;
            color: var(--text-primary);
        }
        
        .summary-item {
            display: flex;
            justify-content: space-between;
            padding: var(--space-2) 0;
            border-bottom: 1px solid var(--border-primary);
        }
        
        .summary-item:last-child {
            border-bottom: none;
        }
        
        .summary-total {
            display: flex;
            justify-content: space-between;
            padding: var(--space-3) 0;
            font-weight: var(--font-weight-bold);
            font-size: var(--font-size-lg);
            color: var(--color-primary);
            border-top: 2px solid var(--border-primary);
            margin-top: var(--space-2);
        }
        
        .customer-details,
        .shipping-details {
            margin-bottom: var(--space-6);
        }
        
        .customer-details h4,
        .shipping-details h4 {
            margin: 0 0 var(--space-4) 0;
            color: var(--text-primary);
            border-bottom: 1px solid var(--border-primary);
            padding-bottom: var(--space-2);
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-4);
        }
        
        .checkout-actions {
            display: flex;
            gap: var(--space-4);
            justify-content: flex-end;
            margin-top: var(--space-6);
            padding-top: var(--space-6);
            border-top: 1px solid var(--border-primary);
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .checkout-actions {
                flex-direction: column;
            }
        }
    `;
    document.head.appendChild(style);
}

// Initialize checkout styles
addCheckoutStyles();
