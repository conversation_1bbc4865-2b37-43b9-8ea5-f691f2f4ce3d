/**
 * Products Page Styles
 * Multi-Tenant Print-on-Demand Platform
 */

/* Dashboard Header Extensions */
.dashboard-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header-actions {
    display: flex;
    gap: var(--space-3);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: var(--space-20) var(--space-8);
    background-color: var(--bg-primary);
    border-radius: var(--radius-xl);
    border: 2px dashed var(--border-secondary);
}

.empty-state-icon {
    font-size: 4rem;
    margin-bottom: var(--space-6);
}

.empty-state h3 {
    margin: 0 0 var(--space-4) 0;
    color: var(--text-primary);
    font-size: var(--font-size-2xl);
}

.empty-state p {
    margin: 0 0 var(--space-8) 0;
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

/* Products Grid */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

/* Product Card */
.product-card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

.product-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

/* Product Image */
.product-image {
    position: relative;
    aspect-ratio: 4/5;
    overflow: hidden;
    background-color: var(--bg-secondary);
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

/* Product Overlay */
.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.product-actions {
    display: flex;
    gap: var(--space-3);
}

.action-btn {
    width: 44px;
    height: 44px;
    border: none;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: var(--radius-full);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: all var(--transition-fast);
    backdrop-filter: blur(10px);
}

.action-btn:hover {
    background-color: white;
    transform: scale(1.1);
}

.action-btn.danger:hover {
    background-color: var(--color-error);
    color: white;
}

/* Product Status */
.product-status {
    position: absolute;
    top: var(--space-3);
    right: var(--space-3);
}

.status-badge {
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-badge.active {
    background-color: var(--green-100);
    color: var(--green-600);
}

.status-badge.inactive {
    background-color: var(--red-100);
    color: var(--red-600);
}

/* Product Info */
.product-info {
    padding: var(--space-6);
}

.product-name {
    margin: 0 0 var(--space-2) 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    line-height: var(--line-height-tight);
}

.product-description {
    margin: 0 0 var(--space-4) 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-normal);
}

/* Product Details */
.product-details {
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-4);
    border-bottom: 1px solid var(--border-primary);
}

.product-price {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-primary);
    margin-bottom: var(--space-2);
}

.product-sizes {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--space-2);
}

.product-colors {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.color-swatches {
    display: flex;
    gap: var(--space-1);
}

.color-swatch {
    width: 20px;
    height: 20px;
    border-radius: var(--radius-full);
    border: 2px solid var(--border-primary);
    cursor: pointer;
    transition: transform var(--transition-fast);
}

.color-swatch:hover {
    transform: scale(1.2);
}

.color-swatch[style*="FFFFFF"] {
    border-color: var(--border-secondary);
}

/* Product Stats */
.product-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-4);
    border-bottom: 1px solid var(--border-primary);
}

.stat {
    text-align: center;
    flex: 1;
}

.stat-value {
    display: block;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
}

.stat-label {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Product Actions Bottom */
.product-actions-bottom {
    display: flex;
    gap: var(--space-3);
}

.product-actions-bottom .btn {
    flex: 1;
    justify-content: center;
}

/* Products Pagination */
.products-pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-6);
    background-color: var(--bg-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
}

.pagination-info {
    margin: 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* Modal Extensions */
.modal-content {
    max-width: 600px;
}

.product-detail-view {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-6);
}

.product-detail-image {
    aspect-ratio: 1;
    border-radius: var(--radius-lg);
    overflow: hidden;
    background-color: var(--bg-secondary);
}

.product-detail-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-detail-info h4 {
    margin: 0 0 var(--space-3) 0;
    color: var(--text-primary);
}

.product-detail-info .detail-item {
    margin-bottom: var(--space-3);
}

.product-detail-info .detail-label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    display: block;
    margin-bottom: var(--space-1);
}

.product-detail-info .detail-value {
    color: var(--text-primary);
}

/* Share Modal */
.share-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--space-4);
    margin-top: var(--space-4);
}

.share-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-4);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    color: var(--text-secondary);
}

.share-option:hover {
    background-color: var(--bg-secondary);
    border-color: var(--border-secondary);
    color: var(--text-primary);
    transform: translateY(-2px);
}

.share-option-icon {
    font-size: 24px;
}

.share-option-label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

.share-link {
    display: flex;
    gap: var(--space-2);
    margin-top: var(--space-4);
}

.share-link input {
    flex: 1;
}

.copy-btn {
    padding: var(--space-3);
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.copy-btn:hover {
    background-color: var(--bg-tertiary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-4);
    }
    
    .header-actions {
        width: 100%;
        justify-content: stretch;
    }
    
    .header-actions .btn {
        flex: 1;
        justify-content: center;
    }
    
    .products-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }
    
    .product-detail-view {
        grid-template-columns: 1fr;
    }
    
    .product-stats {
        flex-wrap: wrap;
        gap: var(--space-3);
    }
    
    .stat {
        flex: 0 0 calc(33.333% - var(--space-2));
    }
    
    .product-actions-bottom {
        flex-direction: column;
    }
    
    .share-options {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .empty-state {
        padding: var(--space-12) var(--space-4);
    }
    
    .empty-state-icon {
        font-size: 3rem;
    }
    
    .product-info {
        padding: var(--space-4);
    }
    
    .product-actions {
        gap: var(--space-2);
    }
    
    .action-btn {
        width: 40px;
        height: 40px;
        font-size: 14px;
    }
    
    .share-options {
        grid-template-columns: 1fr;
    }
}
