# Multi-Tenant Print-on-Demand Platform

A comprehensive full-stack web platform for multi-tenant print-on-demand t-shirt stores.

## Features

- **User Registration & Authentication**: Email/password-based auth with role management
- **T-shirt Design Editor**: Interactive design tool with Fabric.js
- **Multi-tenant Storefronts**: Each user gets their own customizable store
- **Product Management**: Create, manage, and sell custom t-shirt designs
- **Order Processing**: Complete checkout and order management system
- **Super Admin Panel**: Full administrative control over all aspects
- **Responsive Design**: Mobile-first with light/dark mode toggle

## Technology Stack

- **Frontend**: HTML5, CSS3, JavaScript, Fabric.js
- **Backend**: PHP 7+, MySQL 5.7+
- **File Storage**: Local uploads directory
- **Server**: Apache/PHP (XAMPP compatible)

## Project Structure

```
/public-site/          # Landing page
/dashboard/            # User dashboard
/stores/               # Dynamic user stores
/admin/                # Admin panel
/php/                  # Backend logic
/uploads/              # File storage
/assets/               # Static assets
/config/               # Configuration files
/database/             # Database schema
```

## Installation

1. Clone the repository
2. Set up Apache/PHP server (XAMPP recommended)
3. Import database schema from `/database/schema.sql`
4. Configure database connection in `/config/database.php`
5. Set proper permissions for `/uploads/` directory
6. Access the application via your web server

## Default Admin Credentials

- Email: <EMAIL>
- Password: admin123

## Security Notes

- Change default admin credentials immediately
- Ensure `/uploads/` directory has proper permissions
- Configure PHP settings for file upload limits
- Use HTTPS in production

## License

MIT License
