<?php
/**
 * T-shirt Design Editor
 * Multi-Tenant Print-on-Demand Platform
 */

require_once '../config/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('login.php');
}

$user = getCurrentUser();
if (!$user) {
    redirect('login.php');
}

// Get user's store
try {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM stores WHERE user_id = ? LIMIT 1");
    $stmt->execute([$user['id']]);
    $store = $stmt->fetch();
    
    if (!$store) {
        // Create store if doesn't exist
        $storeSlug = DatabaseUtils::sanitizeSlug($user['name'] . '-store');
        $stmt = $db->prepare("INSERT INTO stores (user_id, store_name, slug, description) VALUES (?, ?, ?, ?)");
        $stmt->execute([$user['id'], $user['name'] . "'s Store", $storeSlug, "Welcome to my custom t-shirt store!"]);
        
        $stmt = $db->prepare("SELECT * FROM stores WHERE user_id = ? LIMIT 1");
        $stmt->execute([$user['id']]);
        $store = $stmt->fetch();
    }
} catch (Exception $e) {
    error_log("Store fetch error: " . $e->getMessage());
    $store = null;
}

$pageTitle = 'Design Editor - ' . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/dashboard.css">
    <link rel="stylesheet" href="../assets/css/design-editor.css">
</head>
<body>
    <div class="dashboard-layout">
        <?php include '../includes/dashboard-sidebar.php'; ?>
        
        <main class="dashboard-main">
            <div class="design-editor-container">
                <!-- Editor Header -->
                <div class="editor-header">
                    <div class="editor-title">
                        <h1>T-shirt Design Editor</h1>
                        <p>Create amazing designs for your t-shirt store</p>
                    </div>
                    <div class="editor-actions">
                        <button id="saveDesign" class="btn btn-primary">
                            <i class="icon-save"></i>
                            Save Design
                        </button>
                        <button id="previewDesign" class="btn btn-secondary">
                            <i class="icon-eye"></i>
                            Preview
                        </button>
                    </div>
                </div>
                
                <!-- Editor Layout -->
                <div class="editor-layout">
                    <!-- Tools Panel -->
                    <div class="tools-panel">
                        <div class="tool-section">
                            <h3>Upload Design</h3>
                            <div class="upload-area" id="uploadArea">
                                <input type="file" id="designUpload" accept="image/*" hidden>
                                <div class="upload-content">
                                    <i class="icon-upload"></i>
                                    <p>Click or drag to upload</p>
                                    <small>PNG, JPG, GIF up to 5MB</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="tool-section">
                            <h3>Text Tools</h3>
                            <button id="addText" class="tool-btn">
                                <i class="icon-text"></i>
                                Add Text
                            </button>
                            <div class="text-controls" id="textControls" style="display: none;">
                                <input type="text" id="textInput" placeholder="Enter text" class="form-control">
                                <select id="fontFamily" class="form-control">
                                    <option value="Arial">Arial</option>
                                    <option value="Helvetica">Helvetica</option>
                                    <option value="Times New Roman">Times New Roman</option>
                                    <option value="Georgia">Georgia</option>
                                    <option value="Verdana">Verdana</option>
                                    <option value="Impact">Impact</option>
                                </select>
                                <input type="range" id="fontSize" min="10" max="100" value="30" class="range-slider">
                                <label>Font Size: <span id="fontSizeValue">30</span>px</label>
                            </div>
                        </div>
                        
                        <div class="tool-section">
                            <h3>Colors</h3>
                            <div class="color-picker-container">
                                <input type="color" id="colorPicker" value="#000000">
                                <div class="color-presets">
                                    <div class="color-preset" data-color="#000000" style="background: #000000;"></div>
                                    <div class="color-preset" data-color="#FFFFFF" style="background: #FFFFFF;"></div>
                                    <div class="color-preset" data-color="#FF0000" style="background: #FF0000;"></div>
                                    <div class="color-preset" data-color="#00FF00" style="background: #00FF00;"></div>
                                    <div class="color-preset" data-color="#0000FF" style="background: #0000FF;"></div>
                                    <div class="color-preset" data-color="#FFFF00" style="background: #FFFF00;"></div>
                                    <div class="color-preset" data-color="#FF00FF" style="background: #FF00FF;"></div>
                                    <div class="color-preset" data-color="#00FFFF" style="background: #00FFFF;"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="tool-section">
                            <h3>Actions</h3>
                            <button id="deleteSelected" class="tool-btn btn-danger">
                                <i class="icon-delete"></i>
                                Delete
                            </button>
                            <button id="clearCanvas" class="tool-btn btn-warning">
                                <i class="icon-clear"></i>
                                Clear All
                            </button>
                            <button id="undoAction" class="tool-btn">
                                <i class="icon-undo"></i>
                                Undo
                            </button>
                            <button id="redoAction" class="tool-btn">
                                <i class="icon-redo"></i>
                                Redo
                            </button>
                        </div>
                    </div>
                    
                    <!-- Canvas Area -->
                    <div class="canvas-area">
                        <div class="canvas-container">
                            <div class="tshirt-mockup">
                                <canvas id="designCanvas" width="400" height="500"></canvas>
                            </div>
                        </div>
                        
                        <!-- Canvas Controls -->
                        <div class="canvas-controls">
                            <div class="zoom-controls">
                                <button id="zoomOut" class="control-btn">-</button>
                                <span id="zoomLevel">100%</span>
                                <button id="zoomIn" class="control-btn">+</button>
                            </div>
                            <button id="resetView" class="btn btn-sm">Reset View</button>
                        </div>
                    </div>
                    
                    <!-- Properties Panel -->
                    <div class="properties-panel">
                        <div class="panel-section">
                            <h3>T-shirt Options</h3>
                            <div class="tshirt-colors">
                                <label>T-shirt Color:</label>
                                <div class="color-options">
                                    <?php foreach (TSHIRT_COLORS as $color): ?>
                                        <div class="tshirt-color-option <?php echo $color['name'] === 'White' ? 'active' : ''; ?>" 
                                             data-color="<?php echo $color['hex']; ?>" 
                                             data-name="<?php echo $color['name']; ?>"
                                             style="background-color: <?php echo $color['hex']; ?>;"
                                             title="<?php echo $color['name']; ?>">
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            
                            <div class="size-options">
                                <label>Available Sizes:</label>
                                <div class="size-checkboxes">
                                    <?php foreach (TSHIRT_SIZES as $size): ?>
                                        <label class="checkbox-label">
                                            <input type="checkbox" name="sizes[]" value="<?php echo $size; ?>" checked>
                                            <span class="checkmark"></span>
                                            <?php echo $size; ?>
                                        </label>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="panel-section">
                            <h3>Object Properties</h3>
                            <div id="objectProperties" class="object-properties">
                                <p class="no-selection">Select an object to edit properties</p>
                            </div>
                        </div>
                        
                        <div class="panel-section">
                            <h3>Layers</h3>
                            <div id="layersList" class="layers-list">
                                <!-- Layers will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Save Design Modal -->
    <div id="saveModal" class="modal">
        <div class="modal-backdrop">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Save Design</h3>
                    <button class="modal-close" data-modal-close>&times;</button>
                </div>
                <div class="modal-body">
                    <form id="saveDesignForm">
                        <div class="form-group">
                            <label for="productName">Product Name</label>
                            <input type="text" id="productName" name="productName" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="productDescription">Description</label>
                            <textarea id="productDescription" name="productDescription" class="form-control" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="productPrice">Price (<?php echo DEFAULT_CURRENCY; ?>)</label>
                            <input type="number" id="productPrice" name="productPrice" class="form-control" 
                                   value="<?php echo DEFAULT_PRODUCT_PRICE; ?>" min="1" step="0.01" required>
                        </div>
                        <div class="form-group">
                            <label for="productTags">Tags (comma separated)</label>
                            <input type="text" id="productTags" name="productTags" class="form-control" 
                                   placeholder="e.g. funny, cool, trendy">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-modal-close>Cancel</button>
                    <button type="submit" form="saveDesignForm" class="btn btn-primary">Save Product</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Preview Modal -->
    <div id="previewModal" class="modal">
        <div class="modal-backdrop">
            <div class="modal-content modal-lg">
                <div class="modal-header">
                    <h3>Design Preview</h3>
                    <button class="modal-close" data-modal-close>&times;</button>
                </div>
                <div class="modal-body">
                    <div class="preview-container">
                        <div class="preview-mockup">
                            <canvas id="previewCanvas"></canvas>
                        </div>
                        <div class="preview-info">
                            <h4>Product Details</h4>
                            <div class="preview-details">
                                <p><strong>T-shirt Color:</strong> <span id="previewColor">White</span></p>
                                <p><strong>Available Sizes:</strong> <span id="previewSizes">S, M, L, XL, XXL</span></p>
                                <p><strong>Design Dimensions:</strong> <span id="previewDimensions">400x500px</span></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-modal-close>Close</button>
                    <button type="button" class="btn btn-primary" id="saveFromPreview">Save This Design</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/design-editor.js"></script>
</body>
</html>
