<?php
/**
 * User Dashboard
 * Multi-Tenant Print-on-Demand Platform
 */

require_once '../config/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('login.php');
}

$user = getCurrentUser();
if (!$user) {
    redirect('login.php');
}

// Get dashboard statistics
try {
    $db = getDB();

    // Get user's store
    $stmt = $db->prepare("SELECT * FROM stores WHERE user_id = ? LIMIT 1");
    $stmt->execute([$user['id']]);
    $store = $stmt->fetch();

    // Initialize stats
    $stats = [
        'total_products' => 0,
        'total_orders' => 0,
        'total_revenue' => 0,
        'pending_orders' => 0,
        'active_products' => 0,
        'total_views' => 0
    ];

    if ($store) {
        // Get product statistics
        $stmt = $db->prepare("
            SELECT
                COUNT(*) as total_products,
                SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_products,
                SUM(views_count) as total_views
            FROM products
            WHERE store_id = ?
        ");
        $stmt->execute([$store['id']]);
        $productStats = $stmt->fetch();

        if ($productStats) {
            $stats['total_products'] = $productStats['total_products'];
            $stats['active_products'] = $productStats['active_products'];
            $stats['total_views'] = $productStats['total_views'];
        }

        // Get order statistics
        $stmt = $db->prepare("
            SELECT
                COUNT(*) as total_orders,
                SUM(total_amount) as total_revenue,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_orders
            FROM orders o
            JOIN products p ON o.product_id = p.id
            WHERE p.store_id = ?
        ");
        $stmt->execute([$store['id']]);
        $orderStats = $stmt->fetch();

        if ($orderStats) {
            $stats['total_orders'] = $orderStats['total_orders'];
            $stats['total_revenue'] = $orderStats['total_revenue'] ?: 0;
            $stats['pending_orders'] = $orderStats['pending_orders'];
        }
    }

    // Get recent products
    $recentProducts = [];
    if ($store) {
        $stmt = $db->prepare("
            SELECT * FROM products
            WHERE store_id = ?
            ORDER BY created_at DESC
            LIMIT 6
        ");
        $stmt->execute([$store['id']]);
        $recentProducts = $stmt->fetchAll();
    }

    // Get recent orders
    $recentOrders = [];
    if ($store) {
        $stmt = $db->prepare("
            SELECT o.*, p.name as product_name, p.mockup_path
            FROM orders o
            JOIN products p ON o.product_id = p.id
            WHERE p.store_id = ?
            ORDER BY o.created_at DESC
            LIMIT 5
        ");
        $stmt->execute([$store['id']]);
        $recentOrders = $stmt->fetchAll();
    }

} catch (Exception $e) {
    error_log("Dashboard stats error: " . $e->getMessage());
    $stats = [
        'total_products' => 0,
        'total_orders' => 0,
        'total_revenue' => 0,
        'pending_orders' => 0,
        'active_products' => 0,
        'total_views' => 0
    ];
    $recentProducts = [];
    $recentOrders = [];
}

$pageTitle = 'Dashboard - ' . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/dashboard.css">
</head>
<body>
    <div class="dashboard-layout">
        <?php include '../includes/dashboard-sidebar.php'; ?>

        <main class="dashboard-main">
            <div class="dashboard-header">
                <div>
                    <h1>Welcome back, <?php echo htmlspecialchars($user['name']); ?>!</h1>
                    <p>Here's what's happening with your t-shirt business today.</p>
                </div>
                <div class="header-actions">
                    <a href="design-editor.php" class="btn btn-primary">
                        <i class="icon-design"></i>
                        Create New Design
                    </a>
                </div>
            </div>

            <div class="dashboard-content">
                <!-- Statistics Cards -->
                <div class="dashboard-cards">
                    <div class="dashboard-card">
                        <div class="dashboard-card-header">
                            <h3 class="dashboard-card-title">Total Products</h3>
                            <div class="dashboard-card-icon" style="background-color: var(--primary-100); color: var(--primary-600);">
                                🎨
                            </div>
                        </div>
                        <div class="dashboard-card-value"><?php echo number_format($stats['total_products']); ?></div>
                        <div class="dashboard-card-change">
                            <?php echo $stats['active_products']; ?> active
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="dashboard-card-header">
                            <h3 class="dashboard-card-title">Total Orders</h3>
                            <div class="dashboard-card-icon" style="background-color: var(--secondary-100); color: var(--secondary-600);">
                                📦
                            </div>
                        </div>
                        <div class="dashboard-card-value"><?php echo number_format($stats['total_orders']); ?></div>
                        <div class="dashboard-card-change">
                            <?php echo $stats['pending_orders']; ?> pending
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="dashboard-card-header">
                            <h3 class="dashboard-card-title">Total Revenue</h3>
                            <div class="dashboard-card-icon" style="background-color: var(--green-100); color: var(--green-600);">
                                💰
                            </div>
                        </div>
                        <div class="dashboard-card-value"><?php echo formatPrice($stats['total_revenue']); ?></div>
                        <div class="dashboard-card-change">
                            This month
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="dashboard-card-header">
                            <h3 class="dashboard-card-title">Product Views</h3>
                            <div class="dashboard-card-icon" style="background-color: var(--yellow-100); color: var(--yellow-600);">
                                👁️
                            </div>
                        </div>
                        <div class="dashboard-card-value"><?php echo number_format($stats['total_views']); ?></div>
                        <div class="dashboard-card-change">
                            All time
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions">
                    <h2>Quick Actions</h2>
                    <div class="actions-grid">
                        <a href="design-editor.php" class="action-card">
                            <div class="action-icon">🎨</div>
                            <h3>Create Design</h3>
                            <p>Start designing your next t-shirt masterpiece</p>
                        </a>

                        <a href="products.php" class="action-card">
                            <div class="action-icon">👕</div>
                            <h3>Manage Products</h3>
                            <p>View and edit your product catalog</p>
                        </a>

                        <a href="orders.php" class="action-card">
                            <div class="action-icon">📦</div>
                            <h3>View Orders</h3>
                            <p>Check new orders and update status</p>
                        </a>

                        <a href="store-settings.php" class="action-card">
                            <div class="action-icon">🏪</div>
                            <h3>Store Settings</h3>
                            <p>Customize your store appearance</p>
                        </a>
                    </div>
                </div>

                <!-- Recent Content -->
                <div class="dashboard-sections">
                    <!-- Recent Products -->
                    <div class="dashboard-section">
                        <div class="section-header">
                            <h2>Recent Products</h2>
                            <a href="products.php" class="btn btn-sm btn-outline">View All</a>
                        </div>

                        <?php if (empty($recentProducts)): ?>
                            <div class="empty-section">
                                <div class="empty-icon">🎨</div>
                                <p>No products yet. <a href="design-editor.php">Create your first design!</a></p>
                            </div>
                        <?php else: ?>
                            <div class="products-preview">
                                <?php foreach ($recentProducts as $product): ?>
                                    <div class="product-preview-card">
                                        <div class="product-preview-image">
                                            <img src="<?php echo UPLOAD_URL . '/mockups/' . $product['mockup_path']; ?>"
                                                 alt="<?php echo htmlspecialchars($product['name']); ?>">
                                            <div class="product-preview-overlay">
                                                <a href="products.php" class="preview-btn">View</a>
                                            </div>
                                        </div>
                                        <div class="product-preview-info">
                                            <h4><?php echo htmlspecialchars($product['name']); ?></h4>
                                            <p><?php echo formatPrice($product['base_price']); ?></p>
                                            <span class="status-badge <?php echo $product['is_active'] ? 'active' : 'inactive'; ?>">
                                                <?php echo $product['is_active'] ? 'Active' : 'Inactive'; ?>
                                            </span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Recent Orders -->
                    <div class="dashboard-section">
                        <div class="section-header">
                            <h2>Recent Orders</h2>
                            <a href="orders.php" class="btn btn-sm btn-outline">View All</a>
                        </div>

                        <?php if (empty($recentOrders)): ?>
                            <div class="empty-section">
                                <div class="empty-icon">📦</div>
                                <p>No orders yet. Share your products to get started!</p>
                            </div>
                        <?php else: ?>
                            <div class="orders-list">
                                <?php foreach ($recentOrders as $order): ?>
                                    <div class="order-item">
                                        <div class="order-product">
                                            <img src="<?php echo UPLOAD_URL . '/mockups/' . $order['mockup_path']; ?>"
                                                 alt="<?php echo htmlspecialchars($order['product_name']); ?>">
                                        </div>
                                        <div class="order-details">
                                            <h4><?php echo htmlspecialchars($order['product_name']); ?></h4>
                                            <p>Order #<?php echo $order['order_number']; ?></p>
                                            <p>Customer: <?php echo htmlspecialchars($order['customer_name']); ?></p>
                                        </div>
                                        <div class="order-meta">
                                            <div class="order-amount"><?php echo formatPrice($order['total_amount']); ?></div>
                                            <div class="order-status">
                                                <span class="status-badge status-<?php echo $order['status']; ?>">
                                                    <?php echo ucfirst($order['status']); ?>
                                                </span>
                                            </div>
                                            <div class="order-date"><?php echo formatDate($order['created_at'], 'M d'); ?></div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Store Information -->
                <?php if ($store): ?>
                <div class="store-info-section">
                    <h2>Your Store</h2>
                    <div class="store-info-card">
                        <div class="store-info-header">
                            <div class="store-logo">
                                <?php if ($store['logo_path']): ?>
                                    <img src="<?php echo UPLOAD_URL . '/logos/' . $store['logo_path']; ?>" alt="Store Logo">
                                <?php else: ?>
                                    <div class="default-logo">🏪</div>
                                <?php endif; ?>
                            </div>
                            <div class="store-details">
                                <h3><?php echo htmlspecialchars($store['store_name']); ?></h3>
                                <p><?php echo htmlspecialchars($store['description']); ?></p>
                                <div class="store-url">
                                    <a href="../stores/index.php?store=<?php echo $store['slug']; ?>" target="_blank">
                                        <?php echo SITE_URL; ?>/stores/<?php echo $store['slug']; ?>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="store-stats">
                            <div class="store-stat">
                                <span class="stat-value"><?php echo $store['total_products']; ?></span>
                                <span class="stat-label">Products</span>
                            </div>
                            <div class="store-stat">
                                <span class="stat-value"><?php echo $store['total_orders']; ?></span>
                                <span class="stat-label">Orders</span>
                            </div>
                            <div class="store-stat">
                                <span class="stat-value"><?php echo formatPrice($store['total_revenue']); ?></span>
                                <span class="stat-label">Revenue</span>
                            </div>
                        </div>
                        <div class="store-actions">
                            <a href="store-settings.php" class="btn btn-primary">Customize Store</a>
                            <a href="../stores/index.php?store=<?php echo $store['slug']; ?>" target="_blank" class="btn btn-outline">Visit Store</a>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/dashboard.js"></script>
</body>
</html>
