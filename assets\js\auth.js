/**
 * Authentication JavaScript
 * Multi-Tenant Print-on-Demand Platform
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Password confirmation validation
    const passwordField = document.getElementById('password');
    const confirmPasswordField = document.getElementById('confirm_password');
    
    if (passwordField && confirmPasswordField) {
        function validatePasswordMatch() {
            const password = passwordField.value;
            const confirmPassword = confirmPasswordField.value;
            
            if (confirmPassword && password !== confirmPassword) {
                confirmPasswordField.setCustomValidity('Passwords do not match');
            } else {
                confirmPasswordField.setCustomValidity('');
            }
        }
        
        passwordField.addEventListener('input', validatePasswordMatch);
        confirmPasswordField.addEventListener('input', validatePasswordMatch);
    }
    
    // Password strength indicator
    if (passwordField) {
        const strengthIndicator = createPasswordStrengthIndicator();
        passwordField.parentNode.appendChild(strengthIndicator);
        
        passwordField.addEventListener('input', function() {
            updatePasswordStrength(this.value, strengthIndicator);
        });
    }
    
    // Form submission loading state
    const authForms = document.querySelectorAll('.auth-form');
    authForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitButton = form.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.textContent = 'Please wait...';
                
                // Re-enable after 5 seconds as fallback
                setTimeout(() => {
                    submitButton.disabled = false;
                    submitButton.textContent = submitButton.name === 'login' ? 'Sign In' : 'Create Account';
                }, 5000);
            }
        });
    });
    
    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert-success');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.opacity = '0';
            alert.style.transform = 'translateY(-10px)';
            setTimeout(() => {
                alert.remove();
            }, 300);
        }, 5000);
    });
    
    // Handle URL parameters (like logout message)
    const urlParams = new URLSearchParams(window.location.search);
    const message = urlParams.get('message');
    
    if (message === 'logged_out') {
        showNotification('You have been logged out successfully', 'success');
        // Clean URL
        window.history.replaceState({}, document.title, window.location.pathname);
    }
});

/**
 * Create password strength indicator
 */
function createPasswordStrengthIndicator() {
    const container = document.createElement('div');
    container.className = 'password-strength';
    container.innerHTML = `
        <div class="strength-bar">
            <div class="strength-fill"></div>
        </div>
        <div class="strength-text">Password strength</div>
    `;
    
    // Add CSS for password strength indicator
    if (!document.getElementById('password-strength-styles')) {
        const style = document.createElement('style');
        style.id = 'password-strength-styles';
        style.textContent = `
            .password-strength {
                margin-top: 8px;
            }
            .strength-bar {
                height: 4px;
                background-color: #E5E7EB;
                border-radius: 2px;
                overflow: hidden;
            }
            .strength-fill {
                height: 100%;
                width: 0%;
                transition: all 0.3s ease;
                border-radius: 2px;
            }
            .strength-text {
                font-size: 12px;
                margin-top: 4px;
                color: #6B7280;
            }
            .strength-weak .strength-fill { width: 25%; background-color: #EF4444; }
            .strength-fair .strength-fill { width: 50%; background-color: #F59E0B; }
            .strength-good .strength-fill { width: 75%; background-color: #10B981; }
            .strength-strong .strength-fill { width: 100%; background-color: #059669; }
        `;
        document.head.appendChild(style);
    }
    
    return container;
}

/**
 * Update password strength indicator
 */
function updatePasswordStrength(password, indicator) {
    const strength = calculatePasswordStrength(password);
    const strengthClasses = ['strength-weak', 'strength-fair', 'strength-good', 'strength-strong'];
    
    // Remove all strength classes
    strengthClasses.forEach(cls => indicator.classList.remove(cls));
    
    if (password.length === 0) {
        indicator.querySelector('.strength-text').textContent = 'Password strength';
        return;
    }
    
    // Add appropriate strength class
    if (strength.score >= 4) {
        indicator.classList.add('strength-strong');
        indicator.querySelector('.strength-text').textContent = 'Strong password';
    } else if (strength.score >= 3) {
        indicator.classList.add('strength-good');
        indicator.querySelector('.strength-text').textContent = 'Good password';
    } else if (strength.score >= 2) {
        indicator.classList.add('strength-fair');
        indicator.querySelector('.strength-text').textContent = 'Fair password';
    } else {
        indicator.classList.add('strength-weak');
        indicator.querySelector('.strength-text').textContent = 'Weak password';
    }
}

/**
 * Calculate password strength
 */
function calculatePasswordStrength(password) {
    let score = 0;
    const checks = {
        length: password.length >= 8,
        lowercase: /[a-z]/.test(password),
        uppercase: /[A-Z]/.test(password),
        numbers: /\d/.test(password),
        symbols: /[^A-Za-z0-9]/.test(password)
    };
    
    // Length bonus
    if (password.length >= 8) score++;
    if (password.length >= 12) score++;
    
    // Character variety
    Object.values(checks).forEach(check => {
        if (check) score++;
    });
    
    return {
        score: Math.min(score, 5),
        checks: checks
    };
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Add CSS for notifications if not exists
    if (!document.getElementById('notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 1000;
                animation: slideIn 0.3s ease;
            }
            .notification-success { background-color: #10B981; }
            .notification-error { background-color: #EF4444; }
            .notification-info { background-color: #3B82F6; }
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    }
    
    document.body.appendChild(notification);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideIn 0.3s ease reverse';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

/**
 * Form validation helpers
 */
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

function validateName(name) {
    return name.trim().length >= 2;
}

/**
 * Real-time form validation
 */
document.addEventListener('DOMContentLoaded', function() {
    const emailInputs = document.querySelectorAll('input[type="email"]');
    const nameInputs = document.querySelectorAll('input[name="name"]');
    
    emailInputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value && !validateEmail(this.value)) {
                this.setCustomValidity('Please enter a valid email address');
            } else {
                this.setCustomValidity('');
            }
        });
    });
    
    nameInputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value && !validateName(this.value)) {
                this.setCustomValidity('Name must be at least 2 characters long');
            } else {
                this.setCustomValidity('');
            }
        });
    });
});
