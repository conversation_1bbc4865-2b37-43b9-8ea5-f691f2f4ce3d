# 🚀 PrintShop Platform Setup Guide

## Quick Setup Instructions

### 1. **Database Configuration**

Edit the file `config/database-config.php` with your database settings:

```php
// For XAMPP/WAMP (Windows) - Most Common
define('DB_HOST', 'localhost');
define('DB_NAME', 'printshop_platform');
define('DB_USER', 'root');
define('DB_PASS', '');  // Empty password for XAMPP

// For MAMP (Mac)
define('DB_HOST', 'localhost');
define('DB_NAME', 'printshop_platform');
define('DB_USER', 'root');
define('DB_PASS', 'root');  // MAMP default password

// For Production/Shared Hosting
define('DB_HOST', 'localhost');
define('DB_NAME', 'your_database_name');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### 2. **Run Setup**

1. Make sure your web server (Apache/Nginx) and MySQL are running
2. Open your browser and go to: `http://localhost/your-project-folder/setup.php`
3. Follow the setup wizard

### 3. **Create Admin Account**

After setup completes, the first registered user will automatically become an admin.

---

## 🔧 Troubleshooting Common Issues

### ❌ "Database connection failed"

**Possible Causes:**
- MySQL/MariaDB is not running
- Wrong database credentials
- Database doesn't exist

**Solutions:**
1. **Check if MySQL is running:**
   - XAMPP: Open XAMPP Control Panel → Start MySQL
   - WAMP: Open WAMP → Start MySQL service
   - MAMP: Open MAMP → Start servers

2. **Verify credentials:**
   - Open `config/database-config.php`
   - Check `DB_HOST`, `DB_USER`, `DB_PASS` are correct

3. **Test manually:**
   - Open phpMyAdmin (usually at `http://localhost/phpmyadmin`)
   - Try logging in with your credentials

### ❌ "Session ini settings cannot be changed"

This warning appears when sessions are already started. It's usually harmless but can be fixed by:

1. Make sure no output is sent before session configuration
2. The updated config should handle this automatically

### ❌ "Access denied for user"

**Solutions:**
1. **For XAMPP users:**
   ```php
   define('DB_USER', 'root');
   define('DB_PASS', '');  // Empty password
   ```

2. **For MAMP users:**
   ```php
   define('DB_USER', 'root');
   define('DB_PASS', 'root');  // Default MAMP password
   ```

3. **Create a new MySQL user:**
   ```sql
   CREATE USER 'printshop_user'@'localhost' IDENTIFIED BY 'your_password';
   GRANT ALL PRIVILEGES ON printshop_platform.* TO 'printshop_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

### ❌ "Unknown database 'printshop_platform'"

The setup script will automatically create the database. If it fails:

1. **Manually create database:**
   - Open phpMyAdmin
   - Click "New" → Enter "printshop_platform" → Create

2. **Or use SQL:**
   ```sql
   CREATE DATABASE printshop_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

---

## 📁 File Permissions

Make sure these directories are writable:

```
uploads/
├── designs/     (755 or 777)
├── mockups/     (755 or 777)
├── logos/       (755 or 777)
└── temp/        (755 or 777)
```

**On Linux/Mac:**
```bash
chmod -R 755 uploads/
# or if needed:
chmod -R 777 uploads/
```

**On Windows:**
Usually no action needed, but ensure the web server can write to these folders.

---

## 🌐 Environment-Specific Configurations

### **Local Development (XAMPP/WAMP/MAMP)**
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'printshop_platform');
define('DB_USER', 'root');
define('DB_PASS', '');  // or 'root' for MAMP
```

### **Shared Hosting (cPanel)**
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'cpanel_username_printshop');
define('DB_USER', 'cpanel_username_dbuser');
define('DB_PASS', 'your_database_password');
```

### **VPS/Dedicated Server**
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'printshop_platform');
define('DB_USER', 'printshop_user');
define('DB_PASS', 'secure_password_here');
```

### **Cloud Database (AWS RDS, etc.)**
```php
define('DB_HOST', 'your-db-instance.region.rds.amazonaws.com');
define('DB_NAME', 'printshop_platform');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

---

## ✅ Verification Steps

After setup, verify everything works:

1. **Visit the homepage:** `http://localhost/your-project/`
2. **Register a new account:** Should work without errors
3. **Login to dashboard:** Should redirect to user dashboard
4. **Create a design:** Test the design editor
5. **Admin access:** First user becomes admin automatically

---

## 🆘 Still Having Issues?

### Check PHP Requirements:
- PHP 7.4 or higher
- MySQL 5.7 or higher (or MariaDB 10.2+)
- Required PHP extensions: PDO, PDO_MySQL, GD, JSON

### Enable Error Reporting:
Add this to the top of `setup.php` for debugging:
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

### Check Error Logs:
- **XAMPP:** `xampp/apache/logs/error.log`
- **WAMP:** `wamp/logs/apache_error.log`
- **Linux:** `/var/log/apache2/error.log`

---

## 🎉 Success!

Once setup is complete, you'll have:
- ✅ Working database with all tables
- ✅ Admin account ready
- ✅ File upload directories created
- ✅ Platform ready for users

**Next Steps:**
1. Register your first user account
2. Explore the design editor
3. Create your first t-shirt design
4. Set up your store
5. Start selling! 🚀

---

**Need Help?** Check the configuration files in the `config/` directory for detailed comments and examples.
