<?php
/**
 * Dashboard Sidebar
 * Multi-Tenant Print-on-Demand Platform
 */

$currentUser = getCurrentUser();
$currentPage = basename($_SERVER['PHP_SELF'], '.php');
?>

<aside class="dashboard-sidebar">
    <div class="sidebar-header">
        <div class="logo">
            <h2>PrintShop</h2>
        </div>
        <button class="sidebar-toggle" id="sidebarToggle">
            <span></span>
            <span></span>
            <span></span>
        </button>
    </div>
    
    <div class="sidebar-user">
        <div class="user-avatar">
            <img src="<?php echo $currentUser['avatar_path'] ? UPLOAD_URL . '/avatars/' . $currentUser['avatar_path'] : '../assets/images/default-avatar.png'; ?>" alt="User Avatar">
        </div>
        <div class="user-info">
            <div class="user-name"><?php echo htmlspecialchars($currentUser['name']); ?></div>
            <div class="user-role"><?php echo ucfirst($currentUser['role']); ?></div>
        </div>
    </div>
    
    <nav class="sidebar-nav">
        <ul>
            <li class="nav-item <?php echo $currentPage === 'index' ? 'active' : ''; ?>">
                <a href="index.php">
                    <i class="icon-dashboard"></i>
                    <span>Dashboard</span>
                </a>
            </li>
            
            <li class="nav-item <?php echo $currentPage === 'design-editor' ? 'active' : ''; ?>">
                <a href="design-editor.php">
                    <i class="icon-design"></i>
                    <span>Design Editor</span>
                </a>
            </li>
            
            <li class="nav-item <?php echo $currentPage === 'products' ? 'active' : ''; ?>">
                <a href="products.php">
                    <i class="icon-products"></i>
                    <span>My Products</span>
                </a>
            </li>
            
            <li class="nav-item <?php echo $currentPage === 'orders' ? 'active' : ''; ?>">
                <a href="orders.php">
                    <i class="icon-orders"></i>
                    <span>Orders</span>
                </a>
            </li>
            
            <li class="nav-item <?php echo $currentPage === 'store-settings' ? 'active' : ''; ?>">
                <a href="store-settings.php">
                    <i class="icon-store"></i>
                    <span>Store Settings</span>
                </a>
            </li>
            
            <li class="nav-divider"></li>
            
            <li class="nav-item <?php echo $currentPage === 'profile' ? 'active' : ''; ?>">
                <a href="profile.php">
                    <i class="icon-user"></i>
                    <span>Profile</span>
                </a>
            </li>
            
            <?php if (isAdmin()): ?>
            <li class="nav-item">
                <a href="../admin/dashboard.php">
                    <i class="icon-admin"></i>
                    <span>Admin Panel</span>
                </a>
            </li>
            <?php endif; ?>
            
            <li class="nav-item">
                <a href="logout.php" onclick="return confirm('Are you sure you want to logout?')">
                    <i class="icon-logout"></i>
                    <span>Logout</span>
                </a>
            </li>
        </ul>
    </nav>
    
    <div class="sidebar-footer">
        <div class="theme-toggle">
            <button id="themeToggle" class="theme-toggle-btn">
                <i class="icon-moon"></i>
                <span>Dark Mode</span>
            </button>
        </div>
    </div>
</aside>
