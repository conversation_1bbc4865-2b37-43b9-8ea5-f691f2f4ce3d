<?php
/**
 * Database Configuration Settings
 * Multi-Tenant Print-on-Demand Platform
 * 
 * IMPORTANT: Please update these settings according to your environment
 */

// =============================================================================
// DATABASE CONFIGURATION
// =============================================================================

// Database Host (usually 'localhost' for local development)
define('DB_HOST', 'localhost');

// Database Name (will be created if it doesn't exist)
define('DB_NAME', 'printshop_platform');

// Database Username
define('DB_USER', 'root');

// Database Password (empty for XAMPP/WAMP default)
define('DB_PASS', '');

// Database Charset
define('DB_CHARSET', 'utf8mb4');

// =============================================================================
// COMMON CONFIGURATIONS FOR DIFFERENT ENVIRONMENTS
// =============================================================================

/*
// XAMPP/WAMP (Windows) - Default Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'printshop_platform');
define('DB_USER', 'root');
define('DB_PASS', '');

// MAMP (Mac) - Default Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'printshop_platform');
define('DB_USER', 'root');
define('DB_PASS', 'root');

// LAMP (Linux) - Typical Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'printshop_platform');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');

// Shared Hosting - Typical Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'your_cpanel_username_printshop');
define('DB_USER', 'your_cpanel_username_dbuser');
define('DB_PASS', 'your_database_password');

// Cloud Database (e.g., AWS RDS, Google Cloud SQL)
define('DB_HOST', 'your-database-endpoint.region.rds.amazonaws.com');
define('DB_NAME', 'printshop_platform');
define('DB_USER', 'your_db_username');
define('DB_PASS', 'your_secure_password');
*/

// =============================================================================
// TROUBLESHOOTING GUIDE
// =============================================================================

/*
COMMON ISSUES AND SOLUTIONS:

1. "Database connection failed"
   - Check if MySQL/MariaDB is running
   - Verify DB_HOST, DB_USER, and DB_PASS are correct
   - Make sure the database user has proper permissions

2. "Access denied for user"
   - Check DB_USER and DB_PASS are correct
   - Make sure the user exists in MySQL
   - Grant proper permissions: GRANT ALL PRIVILEGES ON printshop_platform.* TO 'username'@'localhost';

3. "Unknown database"
   - The setup script will try to create the database automatically
   - If it fails, manually create it: CREATE DATABASE printshop_platform;

4. "Connection refused"
   - Check if MySQL service is running
   - Verify the port (default 3306)
   - Check firewall settings

5. For XAMPP users:
   - Start Apache and MySQL from XAMPP Control Panel
   - Use default settings: host=localhost, user=root, password=empty

6. For MAMP users:
   - Start servers from MAMP
   - Default password is usually 'root'

7. For production servers:
   - Use strong passwords
   - Create a dedicated database user
   - Limit permissions to only what's needed
*/

// =============================================================================
// VALIDATION
// =============================================================================

// Basic validation
if (!defined('DB_HOST') || empty(DB_HOST)) {
    die('Error: DB_HOST is not configured. Please check config/database-config.php');
}

if (!defined('DB_NAME') || empty(DB_NAME)) {
    die('Error: DB_NAME is not configured. Please check config/database-config.php');
}

if (!defined('DB_USER')) {
    die('Error: DB_USER is not configured. Please check config/database-config.php');
}

// Note: DB_PASS can be empty for local development

echo "<!-- Database configuration loaded successfully -->\n";
?>
