<?php
/**
 * Database Diagnostic Tool
 * Use this to test database connection and table creation
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/config.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Diagnostic Tool</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #3B82F6;
            background-color: #f8fafc;
        }
        .success { color: #155724; background-color: #d4edda; border-color: #c3e6cb; }
        .error { color: #721c24; background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { color: #856404; background-color: #fff3cd; border-color: #ffeaa7; }
        .info { color: #0c5460; background-color: #d1ecf1; border-color: #bee5eb; }
        pre { background: #f4f4f4; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .btn {
            background-color: #3B82F6;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Database Diagnostic Tool</h1>
        
        <?php
        echo '<div class="test-section">';
        echo '<h3>1. PHP Configuration</h3>';
        echo '<p><strong>PHP Version:</strong> ' . PHP_VERSION . '</p>';
        echo '<p><strong>PDO Available:</strong> ' . (extension_loaded('pdo') ? '✓ Yes' : '✗ No') . '</p>';
        echo '<p><strong>PDO MySQL Available:</strong> ' . (extension_loaded('pdo_mysql') ? '✓ Yes' : '✗ No') . '</p>';
        echo '</div>';
        
        echo '<div class="test-section">';
        echo '<h3>2. Database Configuration</h3>';
        echo '<p><strong>Host:</strong> ' . DB_HOST . '</p>';
        echo '<p><strong>Database:</strong> ' . DB_NAME . '</p>';
        echo '<p><strong>User:</strong> ' . DB_USER . '</p>';
        echo '<p><strong>Password:</strong> ' . (empty(DB_PASS) ? '(empty)' : '(set)') . '</p>';
        echo '</div>';
        
        echo '<div class="test-section">';
        echo '<h3>3. Database Server Connection Test</h3>';
        try {
            $dsn = "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET;
            $pdo = new PDO($dsn, DB_USER, DB_PASS);
            echo '<p class="success">✓ Successfully connected to MySQL server</p>';
            
            $version = $pdo->query('SELECT VERSION()')->fetchColumn();
            echo '<p><strong>MySQL Version:</strong> ' . $version . '</p>';
            
            $supportsJSON = version_compare($version, '5.7.8', '>=');
            echo '<p><strong>JSON Support:</strong> ' . ($supportsJSON ? '✓ Yes' : '✗ No (will use TEXT)') . '</p>';
            
        } catch (Exception $e) {
            echo '<p class="error">✗ Failed to connect to MySQL server</p>';
            echo '<p class="error">Error: ' . $e->getMessage() . '</p>';
        }
        echo '</div>';
        
        echo '<div class="test-section">';
        echo '<h3>4. Database Existence Test</h3>';
        try {
            $dsn = "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET;
            $pdo = new PDO($dsn, DB_USER, DB_PASS);
            
            $stmt = $pdo->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?");
            $stmt->execute([DB_NAME]);
            $dbExists = $stmt->rowCount() > 0;
            
            if ($dbExists) {
                echo '<p class="success">✓ Database "' . DB_NAME . '" exists</p>';
            } else {
                echo '<p class="warning">⚠ Database "' . DB_NAME . '" does not exist</p>';
                echo '<p>Attempting to create database...</p>';
                
                $sql = "CREATE DATABASE IF NOT EXISTS `" . DB_NAME . "` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
                $pdo->exec($sql);
                echo '<p class="success">✓ Database created successfully</p>';
            }
            
        } catch (Exception $e) {
            echo '<p class="error">✗ Database test failed</p>';
            echo '<p class="error">Error: ' . $e->getMessage() . '</p>';
        }
        echo '</div>';
        
        echo '<div class="test-section">';
        echo '<h3>5. Full Database Connection Test</h3>';
        try {
            $db = getDB();
            echo '<p class="success">✓ Successfully connected to database "' . DB_NAME . '"</p>';
            
            $stmt = $db->query("SELECT DATABASE() as current_db");
            $result = $stmt->fetch();
            echo '<p><strong>Current Database:</strong> ' . $result['current_db'] . '</p>';
            
        } catch (Exception $e) {
            echo '<p class="error">✗ Failed to connect to database</p>';
            echo '<p class="error">Error: ' . $e->getMessage() . '</p>';
        }
        echo '</div>';
        
        echo '<div class="test-section">';
        echo '<h3>6. Table Creation Test</h3>';
        try {
            $db = getDB();
            
            // Test creating a simple table
            $testTableSql = "CREATE TABLE IF NOT EXISTS test_table (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $db->exec($testTableSql);
            echo '<p class="success">✓ Test table created successfully</p>';
            
            // Check if table exists
            $stmt = $db->prepare("SHOW TABLES LIKE 'test_table'");
            $stmt->execute();
            if ($stmt->rowCount() > 0) {
                echo '<p class="success">✓ Test table verified in database</p>';
                
                // Clean up test table
                $db->exec("DROP TABLE test_table");
                echo '<p class="info">ℹ Test table cleaned up</p>';
            } else {
                echo '<p class="error">✗ Test table not found after creation</p>';
            }
            
        } catch (Exception $e) {
            echo '<p class="error">✗ Table creation test failed</p>';
            echo '<p class="error">Error: ' . $e->getMessage() . '</p>';
        }
        echo '</div>';
        
        echo '<div class="test-section">';
        echo '<h3>7. Existing Tables Check</h3>';
        try {
            $db = getDB();
            $stmt = $db->query("SHOW TABLES");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            if (empty($tables)) {
                echo '<p class="warning">⚠ No tables found in database</p>';
            } else {
                echo '<p class="success">✓ Found ' . count($tables) . ' tables:</p>';
                echo '<ul>';
                foreach ($tables as $table) {
                    echo '<li>' . $table . '</li>';
                }
                echo '</ul>';
            }
            
        } catch (Exception $e) {
            echo '<p class="error">✗ Could not list tables</p>';
            echo '<p class="error">Error: ' . $e->getMessage() . '</p>';
        }
        echo '</div>';
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_tables'])) {
            echo '<div class="test-section">';
            echo '<h3>8. Manual Table Creation Test</h3>';
            
            $result = createTablesManually();
            if ($result['success']) {
                echo '<p class="success">✓ All tables created successfully</p>';
                if (!empty($result['tables_created'])) {
                    echo '<p>Created tables: ' . implode(', ', $result['tables_created']) . '</p>';
                }
            } else {
                echo '<p class="error">✗ Table creation failed</p>';
                if (!empty($result['error'])) {
                    echo '<p class="error">Error: ' . $result['error'] . '</p>';
                }
            }
            echo '</div>';
        }
        ?>
        
        <div class="test-section">
            <h3>Actions</h3>
            <form method="POST" style="display: inline;">
                <button type="submit" name="create_tables" class="btn">Create Tables Manually</button>
            </form>
            <a href="setup.php" class="btn">Go to Setup</a>
            <a href="index.php" class="btn">Go to Homepage</a>
        </div>
        
        <div class="test-section">
            <h3>Common Solutions</h3>
            <ul>
                <li><strong>If MySQL connection fails:</strong> Check if MySQL/XAMPP is running</li>
                <li><strong>If database doesn't exist:</strong> The tool will try to create it automatically</li>
                <li><strong>If tables aren't created:</strong> Check MySQL user permissions</li>
                <li><strong>For XAMPP users:</strong> Use empty password and 'root' user</li>
                <li><strong>For MAMP users:</strong> Use 'root' user and 'root' password</li>
            </ul>
        </div>
    </div>
</body>
</html>
