<?php
/**
 * Products Management Page
 * Multi-Tenant Print-on-Demand Platform
 */

require_once '../config/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('login.php');
}

$user = getCurrentUser();
if (!$user) {
    redirect('login.php');
}

// Get user's store and products
try {
    $db = getDB();
    
    // Get store
    $stmt = $db->prepare("SELECT * FROM stores WHERE user_id = ? LIMIT 1");
    $stmt->execute([$user['id']]);
    $store = $stmt->fetch();
    
    // Get products
    $products = [];
    if ($store) {
        $stmt = $db->prepare("
            SELECT * FROM products 
            WHERE store_id = ? 
            ORDER BY created_at DESC
        ");
        $stmt->execute([$store['id']]);
        $products = $stmt->fetchAll();
    }
    
} catch (Exception $e) {
    error_log("Products fetch error: " . $e->getMessage());
    $store = null;
    $products = [];
}

$pageTitle = 'My Products - ' . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/dashboard.css">
    <link rel="stylesheet" href="../assets/css/products.css">
</head>
<body>
    <div class="dashboard-layout">
        <?php include '../includes/dashboard-sidebar.php'; ?>
        
        <main class="dashboard-main">
            <div class="dashboard-header">
                <div>
                    <h1>My Products</h1>
                    <p>Manage your t-shirt designs and products</p>
                </div>
                <div class="header-actions">
                    <a href="design-editor.php" class="btn btn-primary">
                        <i class="icon-design"></i>
                        Create New Design
                    </a>
                </div>
            </div>
            
            <div class="dashboard-content">
                <?php if (empty($products)): ?>
                    <div class="empty-state">
                        <div class="empty-state-icon">🎨</div>
                        <h3>No Products Yet</h3>
                        <p>Start creating amazing t-shirt designs to build your product catalog.</p>
                        <a href="design-editor.php" class="btn btn-primary btn-lg">
                            Create Your First Design
                        </a>
                    </div>
                <?php else: ?>
                    <div class="products-grid">
                        <?php foreach ($products as $product): ?>
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="<?php echo UPLOAD_URL . '/mockups/' . $product['mockup_path']; ?>" 
                                         alt="<?php echo htmlspecialchars($product['name']); ?>">
                                    <div class="product-overlay">
                                        <div class="product-actions">
                                            <button class="action-btn" onclick="editProduct(<?php echo $product['id']; ?>)" title="Edit">
                                                ✏️
                                            </button>
                                            <button class="action-btn" onclick="viewProduct(<?php echo $product['id']; ?>)" title="View">
                                                👁️
                                            </button>
                                            <button class="action-btn" onclick="duplicateProduct(<?php echo $product['id']; ?>)" title="Duplicate">
                                                📋
                                            </button>
                                            <button class="action-btn danger" onclick="deleteProduct(<?php echo $product['id']; ?>)" title="Delete">
                                                🗑️
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-status">
                                        <span class="status-badge <?php echo $product['is_active'] ? 'active' : 'inactive'; ?>">
                                            <?php echo $product['is_active'] ? 'Active' : 'Inactive'; ?>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="product-info">
                                    <h3 class="product-name"><?php echo htmlspecialchars($product['name']); ?></h3>
                                    <p class="product-description">
                                        <?php 
                                        $description = htmlspecialchars($product['description']);
                                        echo strlen($description) > 100 ? substr($description, 0, 100) . '...' : $description;
                                        ?>
                                    </p>
                                    
                                    <div class="product-details">
                                        <div class="product-price">
                                            <?php echo formatPrice($product['base_price']); ?>
                                        </div>
                                        <div class="product-sizes">
                                            <?php 
                                            $sizes = json_decode($product['sizes'], true);
                                            echo 'Sizes: ' . implode(', ', $sizes);
                                            ?>
                                        </div>
                                        <div class="product-colors">
                                            <?php 
                                            $colors = json_decode($product['colors'], true);
                                            if ($colors) {
                                                echo '<div class="color-swatches">';
                                                foreach ($colors as $color) {
                                                    echo '<div class="color-swatch" style="background-color: ' . $color['hex'] . ';" title="' . $color['name'] . '"></div>';
                                                }
                                                echo '</div>';
                                            }
                                            ?>
                                        </div>
                                    </div>
                                    
                                    <div class="product-stats">
                                        <div class="stat">
                                            <span class="stat-value"><?php echo $product['views_count']; ?></span>
                                            <span class="stat-label">Views</span>
                                        </div>
                                        <div class="stat">
                                            <span class="stat-value"><?php echo $product['orders_count']; ?></span>
                                            <span class="stat-label">Orders</span>
                                        </div>
                                        <div class="stat">
                                            <span class="stat-value"><?php echo formatDate($product['created_at'], 'M d'); ?></span>
                                            <span class="stat-label">Created</span>
                                        </div>
                                    </div>
                                    
                                    <div class="product-actions-bottom">
                                        <a href="../stores/index.php?store=<?php echo $store['slug']; ?>&product=<?php echo $product['id']; ?>" 
                                           class="btn btn-sm btn-outline" target="_blank">
                                            View in Store
                                        </a>
                                        <button class="btn btn-sm btn-primary" onclick="shareProduct(<?php echo $product['id']; ?>)">
                                            Share
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- Pagination would go here if needed -->
                    <div class="products-pagination">
                        <p class="pagination-info">
                            Showing <?php echo count($products); ?> product<?php echo count($products) !== 1 ? 's' : ''; ?>
                        </p>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>
    
    <!-- Product Actions Modal -->
    <div id="productModal" class="modal">
        <div class="modal-backdrop">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modalTitle">Product Details</h3>
                    <button class="modal-close" data-modal-close>&times;</button>
                </div>
                <div class="modal-body" id="modalBody">
                    <!-- Content will be loaded dynamically -->
                </div>
                <div class="modal-footer" id="modalFooter">
                    <button type="button" class="btn btn-secondary" data-modal-close>Close</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/products.js"></script>
</body>
</html>
