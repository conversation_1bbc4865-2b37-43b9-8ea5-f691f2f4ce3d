<?php
/**
 * Authentication Fix Script
 * Diagnose and fix authentication issues
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/config.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Authentication</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #3B82F6;
            background-color: #f8fafc;
        }
        .success { color: #155724; background-color: #d4edda; border-color: #c3e6cb; }
        .error { color: #721c24; background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { color: #856404; background-color: #fff3cd; border-color: #ffeaa7; }
        .info { color: #0c5460; background-color: #d1ecf1; border-color: #bee5eb; }
        .btn {
            background-color: #3B82F6;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover { background-color: #2563EB; }
        .btn.danger { background-color: #DC2626; }
        .btn.danger:hover { background-color: #B91C1C; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; max-width: 300px; }
        pre { background: #f4f4f4; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Authentication Fix Tool</h1>
        
        <?php
        // Test 1: Check if users table exists and has data
        echo '<div class="test-section">';
        echo '<h3>1. Users Table Check</h3>';
        
        try {
            $db = getDB();
            
            // Check if users table exists
            if (DatabaseUtils::tableExists('users')) {
                echo '<p class="success">✓ Users table exists</p>';
                
                // Count users
                $stmt = $db->query("SELECT COUNT(*) as count FROM users");
                $result = $stmt->fetch();
                echo '<p>Total users: ' . $result['count'] . '</p>';
                
                // Check admin user
                $stmt = $db->prepare("SELECT id, name, email, role, status FROM users WHERE role = 'admin'");
                $stmt->execute();
                $admins = $stmt->fetchAll();
                
                if (empty($admins)) {
                    echo '<p class="error">✗ No admin user found</p>';
                } else {
                    echo '<p class="success">✓ Found ' . count($admins) . ' admin user(s):</p>';
                    foreach ($admins as $admin) {
                        echo '<p>- ' . $admin['name'] . ' (' . $admin['email'] . ') - Status: ' . $admin['status'] . '</p>';
                    }
                }
                
                // Show all users
                $stmt = $db->query("SELECT id, name, email, role, status, created_at FROM users ORDER BY created_at DESC LIMIT 10");
                $users = $stmt->fetchAll();
                
                if (!empty($users)) {
                    echo '<p><strong>Recent users:</strong></p>';
                    echo '<ul>';
                    foreach ($users as $user) {
                        echo '<li>' . $user['name'] . ' (' . $user['email'] . ') - ' . $user['role'] . ' - ' . $user['status'] . '</li>';
                    }
                    echo '</ul>';
                }
                
            } else {
                echo '<p class="error">✗ Users table does not exist</p>';
            }
            
        } catch (Exception $e) {
            echo '<p class="error">✗ Database error: ' . $e->getMessage() . '</p>';
        }
        echo '</div>';
        
        // Test 2: Create/Fix Admin User
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_admin'])) {
            echo '<div class="test-section">';
            echo '<h3>2. Creating Admin User</h3>';
            
            try {
                $db = getDB();
                
                // Delete existing admin if any
                $stmt = $db->prepare("DELETE FROM users WHERE email = '<EMAIL>'");
                $stmt->execute();
                
                // Create new admin user
                $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
                $stmt = $db->prepare("INSERT INTO users (name, email, password, role, email_verified, status) VALUES (?, ?, ?, ?, ?, ?)");
                $stmt->execute([
                    'Super Admin',
                    '<EMAIL>',
                    $adminPassword,
                    'admin',
                    1,
                    'active'
                ]);
                
                echo '<p class="success">✓ Admin user created successfully!</p>';
                echo '<p><strong>Email:</strong> <EMAIL></p>';
                echo '<p><strong>Password:</strong> admin123</p>';
                
            } catch (Exception $e) {
                echo '<p class="error">✗ Error creating admin: ' . $e->getMessage() . '</p>';
            }
            echo '</div>';
        }
        
        // Test 3: Test Registration
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_register'])) {
            echo '<div class="test-section">';
            echo '<h3>3. Testing Registration</h3>';
            
            $testName = $_POST['test_name'];
            $testEmail = $_POST['test_email'];
            $testPassword = $_POST['test_password'];
            
            try {
                require_once 'php/auth.php';
                $result = Auth::register($testName, $testEmail, $testPassword, $testPassword);
                
                if ($result['success']) {
                    echo '<p class="success">✓ Registration successful!</p>';
                    echo '<p>User ID: ' . $result['user_id'] . '</p>';
                    echo '<p>Message: ' . $result['message'] . '</p>';
                } else {
                    echo '<p class="error">✗ Registration failed:</p>';
                    foreach ($result['errors'] as $error) {
                        echo '<p class="error">- ' . $error . '</p>';
                    }
                }
                
            } catch (Exception $e) {
                echo '<p class="error">✗ Registration error: ' . $e->getMessage() . '</p>';
            }
            echo '</div>';
        }
        
        // Test 4: Test Login
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_login'])) {
            echo '<div class="test-section">';
            echo '<h3>4. Testing Login</h3>';
            
            $testEmail = $_POST['login_email'];
            $testPassword = $_POST['login_password'];
            
            try {
                require_once 'php/auth.php';
                $result = Auth::login($testEmail, $testPassword);
                
                if ($result['success']) {
                    echo '<p class="success">✓ Login successful!</p>';
                    echo '<p>User: ' . $result['user']['name'] . ' (' . $result['user']['email'] . ')</p>';
                    echo '<p>Role: ' . $result['user']['role'] . '</p>';
                    echo '<p>Status: ' . $result['user']['status'] . '</p>';
                } else {
                    echo '<p class="error">✗ Login failed:</p>';
                    foreach ($result['errors'] as $error) {
                        echo '<p class="error">- ' . $error . '</p>';
                    }
                }
                
            } catch (Exception $e) {
                echo '<p class="error">✗ Login error: ' . $e->getMessage() . '</p>';
            }
            echo '</div>';
        }
        
        // Test 5: Session Check
        echo '<div class="test-section">';
        echo '<h3>5. Session Status</h3>';
        
        echo '<p><strong>Session Status:</strong> ' . (session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive') . '</p>';
        echo '<p><strong>Session ID:</strong> ' . session_id() . '</p>';
        
        if (isset($_SESSION['logged_in']) && $_SESSION['logged_in']) {
            echo '<p class="success">✓ User is logged in</p>';
            echo '<p>Name: ' . ($_SESSION['user_name'] ?? 'Unknown') . '</p>';
            echo '<p>Email: ' . ($_SESSION['user_email'] ?? 'Unknown') . '</p>';
            echo '<p>Role: ' . ($_SESSION['user_role'] ?? 'Unknown') . '</p>';
        } else {
            echo '<p class="info">ℹ No user logged in</p>';
        }
        
        echo '<p><strong>Session Data:</strong></p>';
        echo '<pre>' . print_r($_SESSION, true) . '</pre>';
        echo '</div>';
        
        // Test 6: Password Hash Test
        echo '<div class="test-section">';
        echo '<h3>6. Password Hash Test</h3>';
        
        $testPassword = 'admin123';
        $hash = password_hash($testPassword, PASSWORD_DEFAULT);
        $verify = password_verify($testPassword, $hash);
        
        echo '<p><strong>Test Password:</strong> ' . $testPassword . '</p>';
        echo '<p><strong>Generated Hash:</strong> ' . substr($hash, 0, 50) . '...</p>';
        echo '<p><strong>Verification:</strong> ' . ($verify ? '✓ Success' : '✗ Failed') . '</p>';
        
        // Test against existing admin password
        try {
            $db = getDB();
            $stmt = $db->prepare("SELECT password FROM users WHERE email = '<EMAIL>'");
            $stmt->execute();
            $admin = $stmt->fetch();
            
            if ($admin) {
                $adminVerify = password_verify('admin123', $admin['password']);
                echo '<p><strong>Admin Password Check:</strong> ' . ($adminVerify ? '✓ Correct' : '✗ Incorrect') . '</p>';
            } else {
                echo '<p class="warning">⚠ Admin user not found</p>';
            }
        } catch (Exception $e) {
            echo '<p class="error">✗ Error checking admin password: ' . $e->getMessage() . '</p>';
        }
        echo '</div>';
        ?>
        
        <!-- Admin Creation Form -->
        <div class="test-section">
            <h3>Create/Fix Admin User</h3>
            <form method="POST">
                <p>This will create a fresh admin user with email: <EMAIL> and password: admin123</p>
                <button type="submit" name="create_admin" class="btn danger" onclick="return confirm('This will replace any existing admin user. Continue?')">Create Admin User</button>
            </form>
        </div>
        
        <!-- Registration Test Form -->
        <div class="test-section">
            <h3>Test Registration</h3>
            <form method="POST">
                <div class="form-group">
                    <label>Name:</label>
                    <input type="text" name="test_name" value="Test User <?php echo rand(1, 999); ?>" required>
                </div>
                <div class="form-group">
                    <label>Email:</label>
                    <input type="email" name="test_email" value="test<?php echo rand(1, 999); ?>@example.com" required>
                </div>
                <div class="form-group">
                    <label>Password:</label>
                    <input type="password" name="test_password" value="testpass123" required>
                </div>
                <button type="submit" name="test_register" class="btn">Test Registration</button>
            </form>
        </div>
        
        <!-- Login Test Form -->
        <div class="test-section">
            <h3>Test Login</h3>
            <form method="POST">
                <div class="form-group">
                    <label>Email:</label>
                    <input type="email" name="login_email" value="<EMAIL>" required>
                </div>
                <div class="form-group">
                    <label>Password:</label>
                    <input type="password" name="login_password" value="admin123" required>
                </div>
                <button type="submit" name="test_login" class="btn">Test Login</button>
            </form>
        </div>
        
        <!-- Quick Actions -->
        <div class="test-section">
            <h3>Quick Actions</h3>
            <a href="dashboard/register.php" class="btn">Real Registration Page</a>
            <a href="dashboard/login.php" class="btn">Real Login Page</a>
            <a href="test-auth.php" class="btn">Auth Test Tool</a>
            <a href="verify-setup.php" class="btn">Setup Verification</a>
            <?php if (isset($_SESSION['logged_in']) && $_SESSION['logged_in']): ?>
                <a href="php/logout.php" class="btn">Logout</a>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
