<?php
/**
 * Main Configuration File
 * Multi-Tenant Print-on-Demand Platform
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Error reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('Asia/Kolkata');

// Site configuration
define('SITE_NAME', 'PrintShop Platform');
define('SITE_URL', 'http://localhost/prject - dream');
define('SITE_DESCRIPTION', 'Multi-tenant print-on-demand platform');
define('SITE_KEYWORDS', 'print on demand, t-shirts, custom designs, online store');

// Directory paths
define('ROOT_PATH', dirname(__DIR__));
define('UPLOAD_PATH', ROOT_PATH . '/uploads');
define('ASSETS_PATH', ROOT_PATH . '/assets');
define('CONFIG_PATH', ROOT_PATH . '/config');
define('PHP_PATH', ROOT_PATH . '/php');

// URL paths
define('BASE_URL', SITE_URL);
define('UPLOAD_URL', BASE_URL . '/uploads');
define('ASSETS_URL', BASE_URL . '/assets');

// File upload settings
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif']);
define('UPLOAD_DESIGNS_PATH', UPLOAD_PATH . '/designs');
define('UPLOAD_MOCKUPS_PATH', UPLOAD_PATH . '/mockups');
define('UPLOAD_LOGOS_PATH', UPLOAD_PATH . '/logos');
define('UPLOAD_AVATARS_PATH', UPLOAD_PATH . '/avatars');

// Security settings
define('SESSION_LIFETIME', 3600 * 24 * 7); // 7 days
define('PASSWORD_MIN_LENGTH', 6);
define('CSRF_TOKEN_NAME', 'csrf_token');

// Default settings
define('DEFAULT_CURRENCY', '₹');
define('DEFAULT_PRODUCT_PRICE', 19.99);
define('DEFAULT_COMMISSION_RATE', 5.00);

// T-shirt configuration
define('TSHIRT_SIZES', ['XS', 'S', 'M', 'L', 'XL', 'XXL']);
define('TSHIRT_COLORS', [
    ['name' => 'White', 'hex' => '#FFFFFF'],
    ['name' => 'Black', 'hex' => '#000000'],
    ['name' => 'Navy Blue', 'hex' => '#1E3A8A'],
    ['name' => 'Red', 'hex' => '#DC2626'],
    ['name' => 'Green', 'hex' => '#16A34A'],
    ['name' => 'Yellow', 'hex' => '#EAB308'],
    ['name' => 'Purple', 'hex' => '#9333EA'],
    ['name' => 'Pink', 'hex' => '#EC4899'],
    ['name' => 'Gray', 'hex' => '#6B7280'],
    ['name' => 'Orange', 'hex' => '#EA580C']
]);

// Email configuration (for future use)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', SITE_NAME);

// Include database configuration
require_once CONFIG_PATH . '/database.php';

// Utility functions
function getCurrentUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    return $protocol . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
}

function redirect($url, $permanent = false) {
    if ($permanent) {
        header('HTTP/1.1 301 Moved Permanently');
    }
    header('Location: ' . $url);
    exit();
}

function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

function isAdmin() {
    return isLoggedIn() && isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    try {
        $db = getDB();
        $stmt = $db->prepare("SELECT * FROM users WHERE id = ? AND status = 'active'");
        $stmt->execute([$_SESSION['user_id']]);
        return $stmt->fetch();
    } catch (Exception $e) {
        return null;
    }
}

function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

function verifyCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

function formatPrice($price) {
    return DEFAULT_CURRENCY . number_format($price, 2);
}

function formatDate($date, $format = 'M d, Y') {
    return date($format, strtotime($date));
}

function generateOrderNumber() {
    return 'ORD-' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6));
}

function createUploadDirectories() {
    $directories = [
        UPLOAD_PATH,
        UPLOAD_DESIGNS_PATH,
        UPLOAD_MOCKUPS_PATH,
        UPLOAD_LOGOS_PATH,
        UPLOAD_AVATARS_PATH
    ];
    
    foreach ($directories as $dir) {
        if (!file_exists($dir)) {
            mkdir($dir, 0755, true);
        }
    }
}

function getFileExtension($filename) {
    return strtolower(pathinfo($filename, PATHINFO_EXTENSION));
}

function isValidImageFile($filename) {
    $extension = getFileExtension($filename);
    return in_array($extension, ALLOWED_IMAGE_TYPES);
}

function resizeImage($source, $destination, $maxWidth = 800, $maxHeight = 600, $quality = 85) {
    $imageInfo = getimagesize($source);
    if (!$imageInfo) return false;
    
    $originalWidth = $imageInfo[0];
    $originalHeight = $imageInfo[1];
    $imageType = $imageInfo[2];
    
    // Calculate new dimensions
    $ratio = min($maxWidth / $originalWidth, $maxHeight / $originalHeight);
    $newWidth = intval($originalWidth * $ratio);
    $newHeight = intval($originalHeight * $ratio);
    
    // Create image resource
    switch ($imageType) {
        case IMAGETYPE_JPEG:
            $sourceImage = imagecreatefromjpeg($source);
            break;
        case IMAGETYPE_PNG:
            $sourceImage = imagecreatefrompng($source);
            break;
        case IMAGETYPE_GIF:
            $sourceImage = imagecreatefromgif($source);
            break;
        default:
            return false;
    }
    
    // Create new image
    $newImage = imagecreatetruecolor($newWidth, $newHeight);
    
    // Preserve transparency for PNG and GIF
    if ($imageType == IMAGETYPE_PNG || $imageType == IMAGETYPE_GIF) {
        imagealphablending($newImage, false);
        imagesavealpha($newImage, true);
        $transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
        imagefilledrectangle($newImage, 0, 0, $newWidth, $newHeight, $transparent);
    }
    
    // Resize image
    imagecopyresampled($newImage, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $originalWidth, $originalHeight);
    
    // Save image
    $result = false;
    switch ($imageType) {
        case IMAGETYPE_JPEG:
            $result = imagejpeg($newImage, $destination, $quality);
            break;
        case IMAGETYPE_PNG:
            $result = imagepng($newImage, $destination);
            break;
        case IMAGETYPE_GIF:
            $result = imagegif($newImage, $destination);
            break;
    }
    
    // Clean up
    imagedestroy($sourceImage);
    imagedestroy($newImage);
    
    return $result;
}

// Set session cookie parameters (before any session starts)
if (session_status() === PHP_SESSION_NONE) {
    ini_set('session.cookie_lifetime', SESSION_LIFETIME);
    ini_set('session.cookie_httponly', 1);
    ini_set('session.use_strict_mode', 1);
}

// Create upload directories on initialization
createUploadDirectories();
?>
