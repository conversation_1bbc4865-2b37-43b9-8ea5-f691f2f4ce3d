/**
 * Admin Panel JavaScript
 * Multi-Tenant Print-on-Demand Platform
 */

document.addEventListener('DOMContentLoaded', function() {
    initAdminPanel();
});

/**
 * Initialize admin panel functionality
 */
function initAdminPanel() {
    initStatsAnimation();
    initAdminActions();
    initRealtimeUpdates();
    console.log('Admin panel initialized');
}

/**
 * Animate statistics cards on load
 */
function initStatsAnimation() {
    const statCards = document.querySelectorAll('.stat-card');
    
    statCards.forEach((card, index) => {
        // Add animation delay based on index
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('animate-slide-up');
        
        // Animate the number counting
        const valueElement = card.querySelector('.stat-value');
        if (valueElement) {
            animateAdminNumber(valueElement);
        }
    });
}

/**
 * Animate number counting effect for admin stats
 */
function animateAdminNumber(element) {
    const finalValue = element.textContent;
    const numericValue = parseFloat(finalValue.replace(/[^0-9.-]+/g, ''));
    
    if (isNaN(numericValue)) return;
    
    const duration = 1500; // 1.5 seconds
    const steps = 40;
    const stepValue = numericValue / steps;
    const stepDuration = duration / steps;
    
    let currentValue = 0;
    let currentStep = 0;
    
    const timer = setInterval(() => {
        currentStep++;
        currentValue += stepValue;
        
        if (currentStep >= steps) {
            currentValue = numericValue;
            clearInterval(timer);
        }
        
        // Format the number based on original format
        let displayValue;
        if (finalValue.includes('₹')) {
            displayValue = '₹' + Math.floor(currentValue).toLocaleString();
        } else if (finalValue.includes(',')) {
            displayValue = Math.floor(currentValue).toLocaleString();
        } else {
            displayValue = Math.floor(currentValue).toString();
        }
        
        element.textContent = displayValue;
    }, stepDuration);
}

/**
 * Initialize admin actions
 */
function initAdminActions() {
    // Add click handlers for action cards
    const actionCards = document.querySelectorAll('.action-card');
    
    actionCards.forEach(card => {
        card.addEventListener('click', function(e) {
            // Add click animation
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });
}

/**
 * Initialize real-time updates for admin dashboard
 */
function initRealtimeUpdates() {
    // Update admin stats every 2 minutes
    setInterval(() => {
        updateAdminStats();
    }, 2 * 60 * 1000);
}

/**
 * Update admin dashboard statistics
 */
function updateAdminStats() {
    fetch('../php/admin/get-stats.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateAdminStatCard('total-users', data.stats.total_users);
                updateAdminStatCard('active-stores', data.stats.active_stores);
                updateAdminStatCard('total-products', data.stats.total_products);
                updateAdminStatCard('total-orders', data.stats.total_orders);
                updateAdminStatCard('total-revenue', data.stats.total_revenue);
            }
        })
        .catch(error => {
            console.error('Error updating admin stats:', error);
        });
}

/**
 * Update individual admin stat card
 */
function updateAdminStatCard(cardId, newValue) {
    const card = document.getElementById(cardId);
    if (!card) return;
    
    const valueElement = card.querySelector('.stat-value');
    if (!valueElement) return;
    
    // Add update animation
    valueElement.style.opacity = '0.5';
    
    setTimeout(() => {
        if (cardId === 'total-revenue') {
            valueElement.textContent = '₹' + parseFloat(newValue).toLocaleString();
        } else {
            valueElement.textContent = parseInt(newValue).toLocaleString();
        }
        
        valueElement.style.opacity = '1';
        
        // Add pulse effect to indicate update
        valueElement.style.animation = 'pulse 0.5s ease';
        setTimeout(() => {
            valueElement.style.animation = '';
        }, 500);
    }, 200);
}

/**
 * View user details
 */
function viewUser(userId) {
    fetch(`../php/admin/get-user.php?id=${userId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showUserModal(data.user);
            } else {
                showNotification(data.message || 'Failed to load user', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('An error occurred while loading user', 'error');
        });
}

/**
 * Show user details modal
 */
function showUserModal(user) {
    const modal = createModal('User Details', `
        <div class="admin-form">
            <div class="form-section">
                <h3>User Information</h3>
                <div class="form-grid">
                    <div class="form-group">
                        <label>Name:</label>
                        <input type="text" value="${user.name}" readonly class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Email:</label>
                        <input type="email" value="${user.email}" readonly class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Role:</label>
                        <input type="text" value="${user.role}" readonly class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Status:</label>
                        <span class="status-badge ${user.is_active ? 'active' : 'inactive'}">
                            ${user.is_active ? 'Active' : 'Inactive'}
                        </span>
                    </div>
                </div>
            </div>
            
            <div class="form-section">
                <h3>Account Details</h3>
                <div class="form-grid">
                    <div class="form-group">
                        <label>Joined:</label>
                        <input type="text" value="${formatDateTime(user.created_at)}" readonly class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Last Login:</label>
                        <input type="text" value="${user.last_login ? formatDateTime(user.last_login) : 'Never'}" readonly class="form-control">
                    </div>
                </div>
            </div>
            
            ${user.store ? `
            <div class="form-section">
                <h3>Store Information</h3>
                <div class="form-grid">
                    <div class="form-group">
                        <label>Store Name:</label>
                        <input type="text" value="${user.store.store_name}" readonly class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Store Status:</label>
                        <span class="status-badge ${user.store.is_active ? 'active' : 'inactive'}">
                            ${user.store.is_active ? 'Active' : 'Inactive'}
                        </span>
                    </div>
                    <div class="form-group">
                        <label>Total Products:</label>
                        <input type="text" value="${user.store.total_products}" readonly class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Total Orders:</label>
                        <input type="text" value="${user.store.total_orders}" readonly class="form-control">
                    </div>
                </div>
            </div>
            ` : ''}
        </div>
    `, [
        { text: 'Close', class: 'btn-secondary', action: 'close' },
        { text: 'Edit User', class: 'btn-primary', action: () => editUser(user.id) }
    ]);
    
    modal.show();
}

/**
 * Edit user
 */
function editUser(userId) {
    // Implementation for editing user
    showNotification('Edit user functionality would be implemented here', 'info');
}

/**
 * Export platform data
 */
function exportPlatformData() {
    const format = prompt('Export format (csv/json/excel):', 'csv');
    
    if (format && ['csv', 'json', 'excel'].includes(format.toLowerCase())) {
        showNotification('Preparing export...', 'info');
        window.location.href = `../php/admin/export-data.php?format=${format.toLowerCase()}`;
    } else if (format) {
        showNotification('Invalid format. Please use csv, json, or excel', 'error');
    }
}

/**
 * Refresh admin dashboard
 */
function refreshDashboard() {
    showNotification('Refreshing dashboard...', 'info');
    setTimeout(() => {
        window.location.reload();
    }, 1000);
}

/**
 * Create a modal dialog
 */
function createModal(title, content, buttons = []) {
    // Remove existing modal if any
    const existingModal = document.getElementById('adminModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    const modal = document.createElement('div');
    modal.id = 'adminModal';
    modal.className = 'modal admin-modal';
    
    const buttonsHtml = buttons.map(btn => 
        `<button type="button" class="btn ${btn.class}" data-action="${btn.action}">${btn.text}</button>`
    ).join('');
    
    modal.innerHTML = `
        <div class="modal-backdrop">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${title}</h3>
                    <button class="modal-close" data-action="close">&times;</button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
                <div class="modal-footer">
                    ${buttonsHtml}
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Add event listeners
    modal.addEventListener('click', function(e) {
        const action = e.target.getAttribute('data-action');
        
        if (action === 'close') {
            modal.remove();
        } else if (typeof action === 'function') {
            action();
        }
    });
    
    return {
        show: () => modal.classList.add('show'),
        hide: () => modal.classList.remove('show'),
        remove: () => modal.remove()
    };
}

/**
 * Format date and time for admin display
 */
function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

/**
 * Toggle user status
 */
function toggleUserStatus(userId, currentStatus) {
    const newStatus = currentStatus ? 0 : 1;
    const action = newStatus ? 'activate' : 'deactivate';
    
    if (!confirm(`Are you sure you want to ${action} this user?`)) {
        return;
    }
    
    fetch('../php/admin/toggle-user-status.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
            user_id: userId, 
            status: newStatus 
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`User ${action}d successfully`, 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showNotification(data.message || `Failed to ${action} user`, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification(`An error occurred while ${action}ing user`, 'error');
    });
}

/**
 * Toggle store status
 */
function toggleStoreStatus(storeId, currentStatus) {
    const newStatus = currentStatus ? 0 : 1;
    const action = newStatus ? 'activate' : 'deactivate';
    
    if (!confirm(`Are you sure you want to ${action} this store?`)) {
        return;
    }
    
    fetch('../php/admin/toggle-store-status.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
            store_id: storeId, 
            status: newStatus 
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`Store ${action}d successfully`, 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showNotification(data.message || `Failed to ${action} store`, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification(`An error occurred while ${action}ing store`, 'error');
    });
}

/**
 * Show system status
 */
function showSystemStatus() {
    fetch('../php/admin/system-status.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const statusHtml = `
                    <div class="system-status">
                        <div class="status-item">
                            <span class="status-indicator">
                                <span class="status-dot ${data.status.database ? 'online' : 'error'}"></span>
                                Database
                            </span>
                            <span>${data.status.database ? 'Connected' : 'Error'}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-indicator">
                                <span class="status-dot ${data.status.uploads ? 'online' : 'error'}"></span>
                                File Uploads
                            </span>
                            <span>${data.status.uploads ? 'Working' : 'Error'}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-indicator">
                                <span class="status-dot ${data.status.email ? 'online' : 'warning'}"></span>
                                Email Service
                            </span>
                            <span>${data.status.email ? 'Working' : 'Not Configured'}</span>
                        </div>
                    </div>
                `;
                
                const modal = createModal('System Status', statusHtml, [
                    { text: 'Close', class: 'btn-secondary', action: 'close' }
                ]);
                
                modal.show();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Failed to load system status', 'error');
        });
}

// Expose functions globally
window.viewUser = viewUser;
window.editUser = editUser;
window.exportPlatformData = exportPlatformData;
window.refreshDashboard = refreshDashboard;
window.toggleUserStatus = toggleUserStatus;
window.toggleStoreStatus = toggleStoreStatus;
window.showSystemStatus = showSystemStatus;
